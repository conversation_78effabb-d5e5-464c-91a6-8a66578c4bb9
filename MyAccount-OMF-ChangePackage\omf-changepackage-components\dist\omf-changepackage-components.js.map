{"version": 3, "file": "omf-changepackage-components.js", "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;ACVa;;AAEb,kBAAkB;AAClB,kBAAe;;AAEf;AACA;AACA;AACA;AACA;AACA;AACA,C;;;;;;;;;;ACXa;;AAEb,kBAAkB;AAClB,kBAAe;;AAEf,yCAAyC,mBAAO,CAAC,+DAAc;;AAE/D,uCAAuC,uCAAuC;;AAE9E;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,C;;;;;;;;;;AC5Ba;;AAEb,kBAAkB;AAClB,kBAAe;;AAEf,wBAAwB,2EAA2E,kCAAkC,wBAAwB,OAAO,kCAAkC,mIAAmI;;AAEzU;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,QAAQ,IAAqC;AAC7C;AACA;;AAEA;AACA;;AAEA;AACA;AACA,wDAAwD;AACxD,QAAQ,IAAqC;AAC7C;AACA;;AAEA;AACA;AACA,C;;;;;;;;;;AC3Ca;;AAEb,kBAAkB;AAClB,kBAAe;;AAEf,+CAA+C,mBAAO,CAAC,2EAAoB;;AAE3E,uCAAuC,uCAAuC;;AAE9E;;AAEA,kBAAe,Y;;;;;;;;;;ACXf,kD;;;;;;;;;;ACAA,mD;;;;;;;;;;ACAA,wD;;;;;;;;;;ACAA,yD;;;;;;;;;;ACAA,mD;;;;;;;;;;ACAA,2D;;;;;;;;;;ACAA,8D;;;;;;;;;;ACAA,kD;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNsD;AACD;AAG9C,IAAU,OAAO,CA8CvB;AA9CD,WAAiB,OAAO;IAET,uBAAe,GAAG,6GAAY,CAAgB,mBAAmB,CAAsD,CAAC;IACxH,sBAAc,GAAG,6GAAY,CAAgC,kBAAkB,CAAsF,CAAC;IACtK,eAAO,GAAG,6GAAY,CAAC,iBAAiB,CAAC,CAAC;IAG1C,sBAAc,GAAG,6GAAY,CAAiB,kBAAkB,CAAiD,CAAC;IAElH,oBAAY,GAAG,6GAAY,CAA4B,eAAe,CAA4E,CAAC;IAGnJ,oBAAY,GAAG,6GAAY,CAA4B,eAAe,CAA8F,CAAC;IACrK,qBAAa,GAAG,6GAAY,CAAS,gBAAgB,CAAwD,CAAC;IAC9G,uBAAe,GAAG,6GAAY,CAAM,mBAAmB,CAA+C,CAAC;IAGvG,uBAAe,GAAG,6GAAY,CAAc,kBAAkB,EAAE,CAAC,UAAC,MAAmB,EAAE,KAAiB;QAAjB,iCAAiB;QAAO,UAAU,CAAC,cAAM,+EAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,wEAAc,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,EAAhG,CAAgG,EAAE,KAAK,CAAC,CAAC;QAAC,OAAO,MAAM,CAAC;IAAC,CAAC,CAAQ,CAA8E,CAAC;IACjV,qBAAa,GAAG,6GAAY,CAAC,gBAAgB,CAAC,CAAC;IAC/C,8BAAsB,GAAG,6GAAY,CAAC,2BAA2B,CAAC,CAAC;IACnE,oCAA4B,GAAG,6GAAY,CAAkC,iCAAiC,CAAsG,CAAC;IACrN,oBAAY,GAAG,6GAAY,CAAe,eAAe,CAAkD,CAAC;IAC5G,iBAAS,GAAG,6GAAY,CAAU,YAAY,CAA0C,CAAC;IAGzF,kBAAU,GAAG,6GAAY,CAAC,qBAAqB,CAAC,CAAC;IACjD,iBAAS,GAAG,6GAAY,CAAS,YAAY,CAAqD,CAAC;IACnG,mBAAW,GAAG,6GAAY,CAAM,cAAc,CAAgD,CAAC;IAC/F,sBAAc,GAAG,6GAAY,CAAC,iBAAiB,CAAC,CAAC;IACjD,wBAAgB,GAAG,6GAAY,CAAC,mBAAmB,CAAC,CAAC;IACrD,uBAAe,GAAG,6GAAY,CAAC,kBAAkB,CAAC,CAAC;IACnD,yBAAiB,GAAG,6GAAY,CAAC,oBAAoB,CAAC,CAAC;IACvD,0BAAkB,GAAG,6GAAY,CAAM,sBAAsB,CAA+C,CAAC;IAC7G,6BAAqB,GAAG,6GAAY,CAAC,0BAA0B,CAAC,CAAC;IAEjE,4BAAoB,GAAG,6GAAY,CAAC,wBAAwB,CAAC,CAAC;IAE9D,wBAAgB,GAAG,6GAAY,CAAoB,sBAAsB,CAA6F,CAAC;IACvK,yBAAiB,GAAG,6GAAY,CAAyB,sBAAsB,CAAoF,CAAC;IACpK,0BAAkB,GAAG,6GAAY,CAAyB,sBAAsB,CAAqF,CAAC;IACtK,2BAAmB,GAAG,6GAAY,CAAM,4BAA4B,CAA6C,CAAC;IAClH,wBAAgB,GAAG,6GAAY,CAAW,oBAAoB,CAAyD,CAAC;IAExH,sBAAc,GAAG,6GAAY,CAA+B,sBAAsB,EAAE,CAAC,UAAC,IAAa,EAAE,IAAU,IAAK,QAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,QAAE,IAAI,QAAE,CAAC,CAAC,CAAC,SAAS,CAAC,EAAnC,CAAmC,CAAQ,CAAqF,CAAC;IACrP,sBAAc,GAAG,6GAAY,CAAS,sBAAsB,CAAsD,CAAC;IACnH,qBAAa,GAAG,6GAAY,CAAS,qBAAqB,CAAsD,CAAC;AAChI,CAAC,EA9CgB,OAAO,KAAP,OAAO,QA8CvB;;;AClDD;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS,gBAAgB,sCAAsC,kBAAkB;AACjF,wBAAwB;AACxB;AACA;;AAEO;AACP;AACA;AACA;AACA,kBAAkB;AAClB;AACA;;AAEO;AACP;AACA,+CAA+C,OAAO;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA,2DAA2D,cAAc;AACzE;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,2CAA2C,QAAQ;AACnD;AACA;;AAEO;AACP,kCAAkC;AAClC;;AAEO;AACP,uBAAuB,uFAAuF;AAC9G;AACA;AACA,yGAAyG;AACzG;AACA,sCAAsC,QAAQ;AAC9C;AACA,gEAAgE;AAChE;AACA,8CAA8C,yFAAyF;AACvI,8DAA8D,2CAA2C;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA,kBAAkB,yBAAyB;AAC3C;AACA;AACA;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA,4CAA4C,yEAAyE;AACrH;;AAEO;AACP;AACA;;AAEO;AACP,0BAA0B,+DAA+D,iBAAiB;AAC1G;AACA,kCAAkC,MAAM,+BAA+B,YAAY;AACnF,iCAAiC,MAAM,mCAAmC,YAAY;AACtF,8BAA8B;AAC9B;AACA,GAAG;AACH;;AAEO;AACP,YAAY,6BAA6B,0BAA0B,cAAc,qBAAqB;AACtG,2IAA2I,cAAc;AACzJ,qBAAqB,sBAAsB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC,iCAAiC,SAAS;AAC1C,iCAAiC,WAAW,UAAU;AACtD,wCAAwC,cAAc;AACtD;AACA,4GAA4G,OAAO;AACnH,+EAA+E,iBAAiB;AAChG,uDAAuD,gBAAgB,QAAQ;AAC/E,6CAA6C,gBAAgB,gBAAgB;AAC7E;AACA,gCAAgC;AAChC;AACA;AACA,QAAQ,YAAY,aAAa,SAAS,UAAU;AACpD,kCAAkC,SAAS;AAC3C;AACA;;AAEO;AACP;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;;AAEM;AACP;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,MAAM;AACxB;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;;AAEA;AACO;AACP,2BAA2B,sBAAsB;AACjD;AACA;AACA;;AAEA;AACO;AACP,gDAAgD,QAAQ;AACxD,uCAAuC,QAAQ;AAC/C,uDAAuD,QAAQ;AAC/D;AACA;AACA;;AAEO;AACP,2EAA2E,OAAO;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;AACA,wMAAwM,cAAc;AACtN,4BAA4B,sBAAsB;AAClD,wBAAwB,YAAY,sBAAsB,qCAAqC,2CAA2C,MAAM;AAChJ,0BAA0B,MAAM,iBAAiB,YAAY;AAC7D,qBAAqB;AACrB,4BAA4B;AAC5B,2BAA2B;AAC3B,0BAA0B;AAC1B;;AAEO;AACP;AACA,eAAe,6CAA6C,UAAU,sDAAsD,cAAc;AAC1I,wBAAwB,6BAA6B,oBAAoB,uCAAuC,kBAAkB;AAClI;;AAEO;AACP;AACA;AACA,yGAAyG,uFAAuF,cAAc;AAC9M,qBAAqB,8BAA8B,gDAAgD,wDAAwD;AAC3J,2CAA2C,sCAAsC,UAAU,mBAAmB,IAAI;AAClH;;AAEO;AACP,+BAA+B,uCAAuC,YAAY,KAAK,OAAO;AAC9F;AACA;;AAEA;AACA,wCAAwC,4BAA4B;AACpE,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,qDAAqD,cAAc;AACnE;AACA;AACA;;AAEO;AACP,2CAA2C;AAC3C;;AAEO;AACP;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,MAAM,oBAAoB,YAAY;AAC5E,qBAAqB,8CAA8C;AACnE;AACA;AACA,qBAAqB,aAAa;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF,SAAS,gBAAgB;AAChH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA,gDAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;;;AChZK;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yB;;ACPP,wBAAwB,2EAA2E,kCAAkC,wBAAwB,OAAO,kCAAkC,mIAAmI;;AAEzU,SAAS,uBAAO,2BAA2B,gCAAgC,oCAAoC,oDAAoD,8DAA8D,iEAAiE,GAAG,kCAAkC;;AAEvU,iCAAiC,gBAAgB,sBAAsB,OAAO,uDAAuD,aAAa,uBAAO,wCAAwC,4CAA4C,KAAK,6CAA6C,6EAA6E,OAAO,uBAAO,kCAAkC,mFAAmF,OAAO;;AAEtf,4CAA4C,kBAAkB,kCAAkC,oEAAoE,KAAK,OAAO,oBAAoB;;AAEpM;AACA;AACA;AACA;AACA;AACe;AACf;;AAEA,iCAAiC,iBAAiB;;;AAGlD;AACA;AACA;AACA,sCAAsC;;AAEtC;AACA,YAAY,KAAqC;AACjD;AACA,QAAQ;;;AAGR;AACA,KAAK;AACL;;AAEA,MAAM,KAAqC;AAC3C;AACA,C;;ACpCoD;AACpD;AACe;AACf;AACA;AACA;AACA;AACA;AACA,iFAAiF,UAAU;AAC3F;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;;AAEA,0DAA0D;;AAE1D;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gDAAgD;;AAEhD,iDAAiD;;AAEjD,qDAAqD;;AAErD,+BAA+B;AAC/B,KAAK,GAAG;AACR;;AAEA;AACA;AACA;AACA;AACA,KAAK,GAAG;;AAER;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,eAAe,aAAoB;AACnC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,KAAK;;;AAGL;AACA;AACA;AACA;AACA,EAAE;;AAEF;AACA;AACA,C;;AC9HyC;AAC1B,SAAS,6BAAc;AACtC;AACA,iFAAiF,UAAU;AAC3F;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;;AAEA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA,QAAQ;AACR,YAAY,KAAqC;AACjD;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA,C;;ACvCyC;AAC1B;AACf;AACA,iFAAiF,UAAU;AAC3F;AACA;;AAEA;AACA,aAAa,aAAoB;AACjC;AACA;AACA,C;;ACXA,SAAS,sBAAO,2BAA2B,gCAAgC,oCAAoC,oDAAoD,8DAA8D,iEAAiE,GAAG,kCAAkC;;AAEvU,SAAS,2BAAa,WAAW,gBAAgB,sBAAsB,OAAO,uDAAuD,aAAa,sBAAO,wCAAwC,6BAAe,6BAA6B,KAAK,6CAA6C,6EAA6E,OAAO,sBAAO,kCAAkC,mFAAmF,OAAO;;AAEtf,SAAS,6BAAe,oBAAoB,kBAAkB,kCAAkC,oEAAoE,KAAK,OAAO,oBAAoB;;AAEpM,sDAAsD,+BAA+B,8DAA8D,YAAY,oCAAoC,6DAA6D,YAAY,6BAA6B,OAAO,2BAA2B,0CAA0C,wEAAwE,+BAA+B;;AAE5d,2DAA2D,+BAA+B,iBAAiB,sCAAsC,YAAY,YAAY,uBAAuB,OAAO,qBAAqB,0CAA0C,6BAA6B;;AAE5M;AACvB;AACd;AACG;AACH;AAClD;AACA;AACA;AACA;AACA;AACA;;AAEe;AACf,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;;AAEA,gEAAgE,eAAe;AAC/E;AACA,+DAA+D,eAAe;AAC9E,gDAAgD,6BAAqB;AACrE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA,6DAA6D;;;AAG7D;;AAEA,wBAAwB,OAAO;AAC/B;;AAEA;AACA;AACA,YAAY,KAAqC,4HAA4H;;AAE7K;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO,YAAY;;AAEnB,uBAAuB;;AAEvB,sCAAsC,gBAAgB,UAAU;;AAEhE;AACA;AACA;AACA,eAAe,2BAAa,GAAG;AAC/B;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT,cAAc,KAAqC;;AAEnD;AACA,SAAS;AACT,OAAO;AACP;AACA,OAAO;AACP,aAAa,2BAAa,GAAG;AAC7B;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM,yBAAyB,KAAK;AACpC;AACA,oBAAoB,gBAAgB;AACpC,aAAa,2BAAa,GAAG;AAC7B;AACA,OAAO;AACP,MAAM,yBAAyB,KAAK;AACpC;AACA,aAAa,2BAAa,GAAG;AAC7B;AACA,OAAO;AACP,MAAM,yBAAyB,KAAK;AACpC;AACA,MAAM,yBAAyB,SAAS;AACxC;AACA,yBAAyB,2BAAa,GAAG;AACzC,kBAAkB,2BAAa,GAAG;AAClC;AACA,SAAS;;AAET,OAAO;;AAEP;AACA;AACA,2CAA2C;;AAE3C;;AAEA,wBAAwB,2BAAa,GAAG;AACxC,oBAAoB,2BAAa,GAAG;AACpC;AACA,WAAW;AACX,SAAS;;AAET;AACA;AACA,MAAM;;;AAGN,sDAAsD;AACtD;;AAEA;AACA;AACA,6BAA6B,2BAAa,GAAG;AAC7C;AACA,KAAK;AACL;AACA,C;;;;ACxJA,SAAS,sBAAO,QAAQ,2EAA2E,sBAAO,2BAA2B,wBAAwB,OAAO,sBAAO,2BAA2B,mIAAmI,OAAO,sBAAO;;AAEvV,SAAS,uBAAO,2BAA2B,gCAAgC,oCAAoC,oDAAoD,8DAA8D,iEAAiE,GAAG,kCAAkC;;AAEvU,SAAS,4BAAa,WAAW,gBAAgB,sBAAsB,OAAO,uDAAuD,aAAa,uBAAO,wCAAwC,8BAAe,6BAA6B,KAAK,6CAA6C,6EAA6E,OAAO,uBAAO,kCAAkC,mFAAmF,OAAO;;AAEtf,SAAS,8BAAe,oBAAoB,kBAAkB,kCAAkC,oEAAoE,KAAK,OAAO,oBAAoB;;AAEpM;AACA;AACA;AACA;AACA;AACA;AACe;AACf;;AAEA,iBAAiB,4BAAa,GAAG,iBAAiB;;;AAGlD,sBAAsB,sBAAO;AAC7B;AACA;AACA,sCAAsC;;AAEtC;AACA,YAAY,KAAqC;AACjD;AACA;;AAEA;AACA;AACA,wBAAwB,4BAAa,GAAG,mBAAmB;AAC3D;AACA,QAAQ;;;AAGR;AACA,KAAK;AACL;;AAEA,MAAM,KAAqC,6BAA6B,sBAAO;AAC/E;AACA;;AAEA;AACA,4CAA4C,sBAAO;AACnD,C;;AC/CwC;AACM;AACkB;AAChE;AACe;AACf,kEAAkE,eAAe;AACjF,SAAS,cAAc,SAAS,iFAAe;AAC/C,C;;ACPA,mCAAmC;;AAEnC,gCAAgC;;AAEhC,kCAAkC;;AAElC,mCAAmC,0BAA0B,8CAA8C,gBAAgB,OAAO,oBAAoB;;AAEtJ,SAAS,oBAAO,2BAA2B,gCAAgC,oCAAoC,oDAAoD,8DAA8D,iEAAiE,GAAG,kCAAkC;;AAEvU,SAAS,yBAAa,WAAW,gBAAgB,sBAAsB,OAAO,uDAAuD,aAAa,oBAAO,wCAAwC,2BAAe,6BAA6B,KAAK,6CAA6C,6EAA6E,OAAO,oBAAO,kCAAkC,mFAAmF,OAAO;;AAEtf,SAAS,2BAAe,oBAAoB,kBAAkB,kCAAkC,oEAAoE,KAAK,OAAO,oBAAoB;;AAEhK;AAC4C;AAChF;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,SAAS,QAAQ;AACjB,aAAa,yBAAa,GAAG;AAC7B;AACA,OAAO;;AAEP,SAAS,SAAS;AAClB;;AAEA;;AAEA;AACA,aAAa,yBAAa,GAAG;AAC7B;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;;AAEe;AACf;AACA,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;;AAEA,gBAAgB,6EAAW;;AAE3B;AACA;AACA,YAAY,QAAQ;AACpB;AACA,KAAK;AACL;;AAEA;AACA;AACA,YAAY,SAAS;AACrB;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,yBAAa,GAAG;AAClC;AACA;AACA;AACA,cAAc,KAAK;AACnB;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA;AACA,cAAc,KAAK;AACnB;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA,cAAc,KAAK;AACnB,OAAO;AACP,KAAK;AACL;AACA;AACA,cAAc,OAAO;AACrB;AACA;AACA,OAAO;AACP;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA,C;;AC9H8C;AAC/B;AACf,yBAAyB;AACzB;;AAEA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;;AAEA,2GAA2G,eAAe;;AAE1H;AACA,UAAU,KAAqC;AAC/C;AACA;;AAEA;AACA,UAAU,IAAqC;AAC/C;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL,QAAQ,KAAqC;;AAE7C;AACA;AACA,YAAY,KAAqC;AACjD;AACA,OAAO;AACP;AACA,MAAM;AACN;AACA;AACA;AACA,C;;AC1Ce;AACf;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,C;;ACrB6D;AACgB;AACpB;AACE;AACI;AACF;AACI;AACA;;;;;;;ACgB1D,SAAS,OAAO,CAAU,IAAS,EAAE,IAAa,EAAE,WAAiB;IAC1E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAAE,OAAO,IAAI,IAAI,WAAW,CAAC;IAC/C,IAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;IAC/C,IAAM,KAAK,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACvD,IAAI,MAAM,GAAG,IAAI,CAAC;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,IAAI,MAAM;eACC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS;eAC9B,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI;eACzB,CAAC,eAAe;mBACZ,CACD,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;uBACjB,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAC7B,CACJ,EAAE,CAAC;YACV,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,WAAW,CAAC;QACvB,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;;;ACpCD,IAAM,cAAc,GAAmB;IACrC,KAAK,EAAE,qCAAqC;IAC5C,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,KAAK;IACpB,IAAI,EAAE,cAAM,YAAK,EAAL,CAAK;CAClB,CAAC;AAsBK,SAAS,MAAM,CAAC,MAAW,EAAE,OAAgC;IAClE,IAAM,KAAK,GAAmB,EAAoB,CAAC;IACnD,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,cAAc,EAAE,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAC,KAAK,EAAE,OAAO,EAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAC/F,IACE,MAAM,KAAK,SAAS;QAChB,KAAK,CAAC,aAAa,IAAI,MAAM,KAAK,IAAI;QACtC,KAAK,CAAC,aAAa,IAAI,MAAM,KAAK,CAAC;QACnC,KAAK,CAAC,eAAe,IAAI,MAAM,KAAK,EAAE;QACtC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;QACtB,MAAM,KAAK,CAAC,KAAK,CAAC;IACpB,OAAO,MAAM,CAAC;AAChB,CAAC;;;;AC/CkD;AAEmB;AACjC;AACH;AACM;AAExC,IAAI,aAAa,GAA6B,EAAE,CAAC;AAEjD,SAAS,iBAAiB,CAAC,WAAyD,EAAE,OAAc;IAAd,wCAAc;IAClG,IAAM,QAAQ,GAAU,EAAE,CAAC;IAC3B,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;QACzB,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;YACxC,CAAC,CAAC,WAAuC;YACzC,CAAC,CAAC,CAAC,WAAW,CAA6B,CAAC;IAChD,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;QACzD,QAAQ,CAAC,IAAI,CACX,OAAO,CAAC,gBAAgB,CAAC,aAAa,CAAC,GAAG,EAAuB,CAAC,CACnE,CAAC;IACJ,CAAC;IACD,IAAI,OAAO,EAAE,CAAC;QACZ,QAAQ,CAAC,IAAI,CACX,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,CAChD,CAAC;IACJ,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAWM,SAAS,2BAA2B,CAAI,QAAwE;IAAE,qBAAsC;SAAtC,UAAsC,EAAtC,qBAAsC,EAAtC,IAAsC;QAAtC,oCAAsC;;IAC7J,MAAM,CAAC,QAAQ,EAAE,kDAAkD,CAAC,CAAC;IACrE,MAAM,CAAC,WAAW,EAAE,yDAAyD,CAAC,CAAC;IAE/E,cAAc,CAAC,OAAO,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IAEhD,QAAQ,IAAI,EAAE,CAAC;QAEb,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC;YACxD,OAAO;gBACL,OAAO,CAAC,YAAY,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,EAAE,sBAAK,CAAC,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAE,GAAG,EAAE,QAAQ,CAAC,GAAG,IAAG,CAAC;gBACtJ,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;aAChD,CAAC;QAEJ,KAAK,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,kBAAkB,EAAE,KAAK,CAAC;YACjD,OAAO,iBAAiB,CAAC,OAAO,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC,CAAC;QAElE;YACE,IAAM,kBAAkB,GAAG,iBAAiB,CAAC,OAAO,CAAC,QAAQ,EAAE,wCAAwC,CAAC,EAAE,KAAK,CAAC,CAAC;YACjH,IAAM,iBAAiB,GAAG;gBACxB,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,4BAA4B,CAAC,OAAO,CAAC,QAAQ,EAAE,sDAAsD,CAAC,CAAC,CAAC;aACzI,CAAC;YAEF,OAAO,gEAAM,8BAEX,4DAAE,wCAAI,kBAAkB,YAExB,4DAAE,wCAAI,iBAAiB,oBAEpB,WAAW,WAAE;IACtB,CAAC;AACH,CAAC;;;;;;;;;ACrEyB;AACW;AAS9B,IAAM,oBAAoB,GAAwC,UAAC,EAIzE;QAHC,EAAE,UACF,cAAW,EAAX,MAAM,mBAAG,EAAE,OACX,cAAc;IAEd,IAAM,IAAI,GAAG,4FAAO,EAAE,CAAC;IAEvB,IAAI,CAAC;QACH,IAAI,WAAS,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,MAAE,cAAc,kBAAE,EAAE,MAAM,CAAC,CAAC;QAGnE,IAAM,aAAa,GAAG,6BAA6B,CAAC;QACpD,WAAS,GAAG,WAAS,CAAC,OAAO,CAAC,aAAa,EAAE,UAAC,CAAC,EAAE,GAAG;YAClD,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC1B,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;oBAC9B,KAAK,EAAE,UAAU;oBACjB,QAAQ,EAAE,KAAK;iBAChB,CAAC,CAAC;YACL,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAGH,IAAI,WAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,WAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,MAAM,EAAE,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAC,EAAY;oBAAZ,kBAAY,EAAX,GAAG,UAAE,KAAK;gBACzC,IAAM,WAAW,GAAG,WAAI,GAAG,MAAG,CAAC;gBAC/B,WAAS,GAAG,WAAS,CAAC,OAAO,CAC3B,IAAI,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,EACnE,MAAM,CAAC,KAAK,CAAC,CACd,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,+FAAM,uBAAuB,EAAE,EAAE,MAAM,EAAE,WAAS,EAAE,GAAI,CAAC;IAClE,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CAAC,yCAAkC,EAAE,CAAE,EAAE,CAAC,CAAC,CAAC;QACxD,IAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,cAAc,IAAI,EAAE,CAAC;QAC/D,OAAO,+FAAM,uBAAuB,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE,GAAI,CAAC;IACrE,CAAC;AACH,CAAC,CAAC;;;;ACjD6C;AACiB;AACR;AACJ;AACZ;AAEjC,IAAU,KAAK,CA8MrB;AA9MD,WAAiB,KAAK;IAWpB,SAAgB,gBAAgB,CAAC,YAA+B;QAC9D,IAAM,MAAM,GAAW,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxD,OAAO,sBAAK,YAAY,KAAE,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,MAAM,CAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAQ,IAAG;IAC3H,CAAC;IAHe,sBAAgB,mBAG/B;IAQD,SAAgB,YAAY,CAAC,UAAkB;QAC7C,IAAM,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC;QAClC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAHe,kBAAY,eAG3B;IACD,SAAgB,cAAc,CAAC,UAAkB;QAC/C,IAAM,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC;QAClC,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAHe,oBAAc,iBAG7B;IACD,SAAgB,YAAY,CAAC,UAAkB;QAC7C,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/B,IAAM,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC;YAClC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IALe,kBAAY,eAK3B;IAED,SAAgB,SAAS,CAAC,KAAa;QACrC,IAAM,aAAa,GAAG,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC1D,IAAM,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC;QACzB,IAAM,EAAE,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACd,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC3B,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC;YACD,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC1B,OAAO,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAde,eAAS,YAcxB;IAEY,WAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IACzE,YAAM,GAAG,CAAC,CAAC,MAAM,CAAC,oBAAoB,IAAI,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC;IAElE,gBAAU,GAAG;QACxB,IAAM,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,CAAC;QAC3C,IAAM,QAAQ,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAClD,IAAM,QAAQ,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAClD,OAAO;YACL,QAAQ;YACR,QAAQ;SACT,CAAC;IACJ,CAAC,CAAC;IAEF,SAAgB,QAAQ,CAAC,IAAc,EAAE,IAAY,EAAE,SAAmB;QACxE,IAAI,OAAY,CAAC;QACjB,OAAO;YACL,IAAM,OAAO,GAAG,IAAI,EAAE,IAAI,GAAG,SAAS,CAAC;YACvC,IAAM,KAAK,GAAG;gBACZ,OAAO,GAAG,IAAI,CAAC;gBACf,IAAI,CAAC,SAAS;oBAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAC5C,CAAC,CAAC;YACF,IAAM,OAAO,GAAG,SAAS,IAAI,CAAC,OAAO,CAAC;YACtC,YAAY,CAAC,OAAO,CAAC,CAAC;YACtB,OAAO,GAAG,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAClC,IAAI,OAAO;gBAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;IAbe,cAAQ,WAavB;IA0BY,aAAO,GAAG,UAAC,MAAW,EAAE,QAAa,IAAU,qBAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAhC,CAAgC,CAAC;IAEhF,wBAAkB,GAAG,UAAC,MAAW,IAAc,cAAO,CAAC,YAAY,CAAC,OAAO,CAAC,cAAO,MAAM,CAAC,GAAG,CAAE,CAAC,CAAC,EAAlD,CAAkD,CAAC;IAElG,sBAAgB,GAAG,UAAC,OAAiB;QAChD,OAAO,CAAC,OAAO,CAAC,UAAC,MAAc,IAAK,qBAAc,CAAC,UAAU,CAAC,cAAO,MAAM,CAAE,CAAC,EAA1C,CAA0C,CAAC,CAAC;IAClF,CAAC,CAAC;IAKW,mBAAa,GAAQ,UAAC,IAAY,EAAE,SAA6B;QAA7B,0CAA6B;QAAK,QAAC;YAClF,OAAO,EAAE,CAAC;YACV,SAAS,EAAE,MAAM;YACjB,OAAO;YACP,eAAe,EAAE,kBAAO;YACxB,GAAG,EAAE,IAAI;YACT,SAAS,EAAE,eAAC,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE,aAAa,UAAK,SAAS,SAAC;SAClG,CAAC;IAPiF,CAOjF,CAAC;IAEH,SAAgB,WAAW;QACzB,IAAI,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,CAAc,CAAC;QACnE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC1C,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,QAAQ,CAAC,OAAO,CAAC,wBAAwB,CAAC,GAAG,CAAC;oBACjD,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;oBAC9B,MAAM;gBACR,KAAK,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC;oBAC3C,QAAQ,GAAG,SAAS,CAAC,EAAE,CAAC;oBACxB,MAAM;gBACR,KAAK,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;oBACjC,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC;oBAC3B,MAAM;gBACR,KAAK,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC;oBAClC,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;oBAC5B,MAAM;YACV,CAAC;QACH,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IApBe,iBAAW,cAoB1B;IAED,SAAgB,kBAAkB,CAAC,KAAoB,EAAE,QAAoB;QAC3E,QAAQ,GAAG,QAAQ,IAAI,WAAW,EAAE,CAAC;QACrC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,SAAS,CAAC,QAAQ;gBACrB,QAAQ,KAAK,EAAE,CAAC;oBACd,KAAK,YAAY,CAAC,QAAQ,CAAC,CAAC,OAAO,yBAAyB,CAAC;oBAC7D,KAAK,YAAY,CAAC,WAAW,CAAC,CAAC,OAAO,qCAAqC,CAAC;oBAC5E,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC,OAAO,gCAAgC,CAAC;oBAClE,KAAK,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO,sCAAsC,CAAC;oBAC9E,OAAO,CAAC,CAAC,OAAO,gBAAgB,CAAC;gBACnC,CAAC;YACH,KAAK,SAAS,CAAC,EAAE;gBACf,QAAQ,KAAK,EAAE,CAAC;oBACd,KAAK,YAAY,CAAC,EAAE,CAAC,CAAC,OAAO,mBAAmB,CAAC;oBACjD,KAAK,YAAY,CAAC,WAAW,CAAC,CAAC,OAAO,+BAA+B,CAAC;oBACtE,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC,OAAO,0BAA0B,CAAC;oBAC5D,KAAK,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO,gCAAgC,CAAC;oBACxE,OAAO,CAAC,CAAC,OAAO,gBAAgB,CAAC;gBACnC,CAAC;YACH,KAAK,SAAS,CAAC,KAAK;gBAClB,QAAQ,KAAK,EAAE,CAAC;oBACd,KAAK,YAAY,CAAC,EAAE,CAAC,CAAC,OAAO,SAAS,CAAC;oBACvC,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC,OAAO,gBAAgB,CAAC;oBAClD,KAAK,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO,sBAAsB,CAAC;oBAC9D,OAAO,CAAC,CAAC,OAAO,MAAM,CAAC;gBACzB,CAAC;YACH,KAAK,SAAS,CAAC,MAAM;gBACnB,QAAQ,KAAK,EAAE,CAAC;oBACd,KAAK,YAAY,CAAC,QAAQ,CAAC,CAAC,OAAO,kBAAkB,CAAC;oBACtD,KAAK,YAAY,CAAC,EAAE,CAAC,CAAC,OAAO,YAAY,CAAC;oBAC1C,KAAK,YAAY,CAAC,WAAW,CAAC,CAAC,OAAO,8BAA8B,CAAC;oBACrE,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC,OAAO,gBAAgB,CAAC;oBAClD,KAAK,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO,sBAAsB,CAAC;oBAC9D,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;gBAC5B,CAAC;QACL,CAAC;IACH,CAAC;IApCe,wBAAkB,qBAoCjC;IAED,SAAgB,YAAY;QAC1B,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAC1C,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,YAAY,CAAC,MAAM,CAAC;YAChE,KAAK,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,YAAY,CAAC,YAAY,CAAC;YACvE,KAAK,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,YAAY,CAAC,WAAW,CAAC;YACtE,KAAK,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,YAAY,CAAC,QAAQ,CAAC;YACpE,KAAK,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,YAAY,CAAC,EAAE,CAAC;QAC1D,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAVe,kBAAY,eAU3B;IAED,SAAgB,gBAAgB,CAAC,MAAiC;QAChE,IAAM,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,UAAU,CAAC;QACtE,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAHe,sBAAgB,mBAG/B;IAED,SAAgB,iBAAiB,CAAC,IAAY;QAC5C,IAAM,UAAU,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC7D,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACjE,CAAC;IAHe,uBAAiB,oBAGhC;AACH,CAAC,EA9MgB,KAAK,KAAL,KAAK,QA8MrB;AACwB;AACK;AACgB;AAEP;;;ACzNhC,IAAU,IAAI,CA2UpB;AA3UD,WAAiB,IAAI;IAyDnB,IAAY,cAiBX;IAjBD,WAAY,cAAc;QAExB,6BAAW;QACX,mCAAiB;QACjB,mCAAiB;QACjB,uCAAqB;QAErB,mCAAiB;QACjB,mCAAiB;QACjB,yDAAuC;QACvC,iDAA+B;QAC/B,qCAAmB;QACnB,6CAA2B;QAC3B,iCAAe;QACf,2CAAyB;QACzB,mCAAiB;QACjB,uCAAqB;IACvB,CAAC,EAjBW,cAAc,GAAd,mBAAc,KAAd,mBAAc,QAiBzB;IAED,IAAY,aAIX;IAJD,WAAY,aAAa;QACvB,8CAA6B;QAC7B,gDAA+B;QAC/B,kDAAiC;IACnC,CAAC,EAJW,aAAa,GAAb,kBAAa,KAAb,kBAAa,QAIxB;IAuBD,IAAY,gBAgBX;IAhBD,WAAY,gBAAgB;QAC1B,uDAAmC;QACnC,yCAAqB;QACrB,mCAAe;QACf,6BAAS;QACT,2DAAuC;QACvC,qCAAiB;QACjB,mDAA+B;QAC/B,iEAA6C;QAC7C,qEAAiD;QACjD,yDAAqC;QACrC,+DAA2C;QAC3C,qCAAiB;QACjB,mDAA+B;QAC/B,2CAAuB;QACvB,iCAAa;IACf,CAAC,EAhBW,gBAAgB,GAAhB,qBAAgB,KAAhB,qBAAgB,QAgB3B;IAED,IAAY,oBAKX;IALD,WAAY,oBAAoB;QAC9B,+CAAuB;QACvB,uCAAe;QACf,2CAAmB;QACnB,qCAAa;IACf,CAAC,EALW,oBAAoB,GAApB,yBAAoB,KAApB,yBAAoB,QAK/B;IA4DD,IAAY,yBAKX;IALD,WAAY,yBAAyB;QACnC,4CAAe;QACf,wCAAW;QACX,gDAAmB;QACnB,gDAAmB;IACrB,CAAC,EALW,yBAAyB,GAAzB,8BAAyB,KAAzB,8BAAyB,QAKpC;IAED,IAAY,eAGX;IAHD,WAAY,eAAe;QACzB,4BAAS;QACT,wCAAqB;IACvB,CAAC,EAHW,eAAe,GAAf,oBAAe,KAAf,oBAAe,QAG1B;IAeD,IAAY,oBAWX;IAXD,WAAY,oBAAoB;QAC9B,iCAAS;QACT,iCAAS;QACT,2CAAmB;QACnB,yCAAiB;QACjB,6CAAqB;QACrB,6CAAqB;QACrB,6CAAqB;QACrB,6CAAqB;QACrB,6CAAqB;QACrB,6CAAqB;IACvB,CAAC,EAXW,oBAAoB,GAApB,yBAAoB,KAApB,yBAAoB,QAW/B;IAED,IAAY,uBAIX;IAJD,WAAY,uBAAuB;QACjC,0CAAe;QACf,uDAA4B;QAC5B,0CAAe;IACjB,CAAC,EAJW,uBAAuB,GAAvB,4BAAuB,KAAvB,4BAAuB,QAIlC;AAuGH,CAAC,EA3UgB,IAAI,KAAJ,IAAI,QA2UpB;;;;AC3UkD;AAClB;AAEI;AAGd;AAEvB,IAAY,OAEX;AAFD,WAAY,OAAO;IACjB,yCAA8B;AAChC,CAAC,EAFW,OAAO,KAAP,OAAO,QAElB;AAED,IAAY,aAMX;AAND,WAAY,aAAa;IACvB,iDAAI;IACJ,yDAAQ;IACR,yDAAQ;IACR,mDAAK;IACL,6DAAU;AACZ,CAAC,EANW,aAAa,KAAb,aAAa,QAMxB;AAED,IAAY,YAeX;AAfD,WAAY,YAAY;IACtB,sCAAsB;IACtB,0BAAU;IACV,yCAAyB;IACzB,2CAA2B;IAC3B,qCAAqB;IACrB,yCAAyB;IACzB,mDAAmC;IACnC,gEAAgD;IAChD,oEAAoD;IACpD,qCAAqB;IACrB,qCAAqB;IACrB,4CAA4B;IAC5B,kCAAkB;IAClB,8CAA8B;AAChC,CAAC,EAfW,YAAY,KAAZ,YAAY,QAevB;AAED,IAAY,WAIX;AAJD,WAAY,WAAW;IACrB,kCAAmB;IACnB,gCAAiB;IACjB,4CAA6B;AAC/B,CAAC,EAJW,WAAW,KAAX,WAAW,QAItB;AAED,IAAY,WASX;AATD,WAAY,WAAW;IACrB,0DAA2C;IAC3C,sDAAuC;IACvC,0CAA2B;IAC3B,4DAA6C;IAE7C,mDAAoC;IACpC,kDAAmC;IACnC,wDAAyC;AAC3C,CAAC,EATW,WAAW,KAAX,WAAW,QAStB;AAED,IAAY,SAKX;AALD,WAAY,SAAS;IACnB,kCAAqB;IACrB,sBAAS;IACT,4BAAe;IACf,8BAAiB;AACnB,CAAC,EALW,SAAS,KAAT,SAAS,QAKpB;AAqBM,IAAU,MAAM,CAiLtB;AAjLD,WAAiB,MAAM;IAOrB,IAAY,MAIX;IAJD,WAAY,MAAM;QAChB,oBAAU;QACV,sBAAY;QACZ,qBAAW;IACb,CAAC,EAJW,MAAM,GAAN,aAAM,KAAN,aAAM,QAIjB;IAgHD;QAAkC,gCAAK;QAKrC,sBAAY,OAAuB,EAAE,OAAa,EAAE,KAAsB;YAAtB,qCAAsB;YACxE,kBAAK,YAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,SAAC;YAFjE,WAAK,GAAY,KAAK,CAAC;YAGrB,KAAI,CAAC,KAAK,GAAG,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC;YAC9D,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAChC,KAAI,CAAC,KAAK,GAAI,OAAiB,CAAC,KAAK,CAAC;YACxC,CAAC;YACD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAChC,QAAQ,IAAI,EAAE,CAAC;oBACb,KAAK,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC1B,KAAI,CAAC,IAAI,GAAG,KAAK,CAAC;wBAClB,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;wBACxB,MAAM;oBACR,KAAK,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;wBACrC,KAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;wBACrB,KAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;wBAC7C,MAAM;gBACV,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,KAAI,CAAC,IAAI,GAAG,OAAO,CAAC;YACtB,CAAC;;QACH,CAAC;QACH,mBAAC;IAAD,CAAC,CA1BiC,KAAK,GA0BtC;IA1BY,mBAAY,eA0BxB;IAED,SAAgB,sBAAsB,CAAC,MAAW;QAChD,OAAO,UAAU,QAA0B,EAAE,MAA4B;YACvE,IAAM,GAAG,GAAU,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC;YAC9C,OAAO,mEAAK,CACV,gEAAE,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,EAClE,MAAM,CACP,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IARe,6BAAsB,yBAQrC;IAGY,yBAAkB,GAAG,MAAM,CACtC,iBAAiB,CAClB,CAAC;IACW,iBAAU,GAAG,MAAM,CAC9B,2IAA2I,CAC5I,CAAC;IACW,iBAAU,GAAG,MAAM,CAC9B,2BAA2B,CAC5B,CAAC;IACW,qBAAc,GAAG,MAAM,CAClC,SAAS,CACV,CAAC;IACW,uBAAgB,GAAG,MAAM,CACpC,WAAW,CACZ,CAAC;AACJ,CAAC,EAjLgB,MAAM,KAAN,MAAM,QAiLtB;;;;AClQ4E;AAEjC;AACH;AACC;AAE1C,IAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC;AAEzD,SAAS,uBAAuB,CAAC,MAA0B;IACzD,OAAO,IAAI,oEAAU,CAAC,UAAC,QAAuB;QAC5C,MAAM,EAAE;aACL,IAAI,CAAC,kBAAQ;YACZ,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC,CAAC;aACD,KAAK,CAAC,eAAK;YACV,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC,CAAC;aACD,IAAI,CAAC;YACJ,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,aAAa,CAAC,IAAS;IAC9B,OAAO,uBAAuB,CAAC,cAAM,WAAI,OAAO,CAAM,UAAC,OAAO;QAC5D,UAAU,CAAC,cAAM,cAAO,CAAC,EAAE,IAAI,QAAE,CAAC,EAAjB,CAAiB,EAAE,GAAG,CAAC,CAAC;IAC3C,CAAC,CAAC,EAFmC,CAEnC,CAAC,CAAC;AACN,CAAC;AAED,SAAS,SAAS,CAAC,IAAY;IAC7B,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAY,CAAC;AACjD,CAAC;AAWD;IAAyC,8BAAyB;IAOhE,oBAAY,IAAkB,EAAU,MAA0B;QAChE,kBAAK,YAAC,IAAI,CAAC,SAAC;QAD0B,YAAM,GAAN,MAAM,CAAoB;;IAElE,CAAC;IACD,sBAAY,mCAAW;aAAvB;YACE,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAC/C,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC/F,CAAC;;;OAAA;IAED,sBAAY,gCAAQ;aAApB;YACE,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QACpC,CAAC;;;OAAA;IAGM,wBAAG,GAAV,UAAc,IAAY,EAAE,KAAW,EAAE,QAAkC;QACzE,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,KAAK,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC,WAAW;YACf,IAAI,CAAC,CAAC;YACZ,aAAa,CAAC,IAAI,CAAQ;YAC1B,CAAC,CAAC,gBAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAQ,CAAC;IAC9C,CAAC;IAGM,wBAAG,GAAV,UAAc,IAAY,EAAE,KAAU,EAAE,QAAkC;QACxE,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,KAAK,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC,WAAW;YACf,IAAI,CAAC,CAAC;YACZ,aAAa,CAAC,IAAI,CAAQ;YAC1B,CAAC,CAAC,gBAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAQ,CAAC;IAC9C,CAAC;IAGM,yBAAI,GAAX,UAAe,IAAY,EAAE,KAAU,EAAE,QAAkC;QACzE,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC,WAAW;YACf,IAAI,CAAC,CAAC;YACZ,aAAa,CAAC,IAAI,CAAQ;YAC1B,CAAC,CAAC,gBAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAQ,CAAC;IAC/C,CAAC;IAGM,wBAAG,GAAV,UAAW,IAAY,EAAE,QAAkC;QACzD,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,EAAE,KAAK,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC,WAAW;YACf,IAAI,CAAC,CAAC;YACZ,aAAa,CAAC,IAAI,CAAQ;YAC1B,CAAC,CAAC,gBAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAQ,CAAC;IAC9C,CAAC;IAEM,2BAAM,GAAb,UAAiB,MAA8B;QAC7C,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;YACtB,KAAK,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;YAC7D,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;YAC/D,KAAK,QAAQ,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAQ,CAAC;YACnD,KAAK,KAAK,CAAC;YACX;gBACE,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,sBAAI,+BAAO;aAAX;YACE,OAAO;gBACL,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI;gBACzB,KAAK,EAAE,KAAK;gBACZ,WAAW,EAAE,SAA+B;gBAC5C,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;aAC7B,CAAC;QACJ,CAAC;;;OAAA;IAzEmB,UAAU;QAD/B,oEAAU;yCAQS,sEAAY;OAPV,UAAU,CA0E/B;IAAD,iBAAC;CAAA,CA1EwC,wEAAc,CAAC,UAAU,GA0EjE;AA1E+B;;;;AC1CD;AAWxB,IAAM,aAAa,GAAG,2EAAmB,CAAkD,EAA+C,CAAC,CAAC;AAE5I,IAAM,eAAe,GAAwG,aAAa,CAAC,QAAQ,CAAC;AACpJ,IAAM,OAAO,GAAgG,aAAa,CAAC,QAAQ,CAAC;AACpI,SAAS,WAAW,CACzB,SAAc;IAEd,OAAO,UAAU,KAAQ;QACvB,OAAO,CAAC,4EAAC,OAAO,QACb,UAAC,OAAmC,IAAK,mFAAC,SAAS,eAAK,KAAK,EAAM,OAAO,EAAI,EAArC,CAAqC,CACvE,CAAC,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;;;;;ACvB6D;AACzB;AACA;AACa;AACjB;AAG/B,mBAAe,GAIb,OAAO,gBAJM,EACf,cAAc,GAGZ,OAAO,eAHK,EACd,YAAY,GAEV,OAAO,aAFG,EACZ,gBAAgB,GACd,OAAO,iBADO,CACN;AASZ;IAAA;IA+DA,CAAC;IA9DC,qCAAY,GAAZ;QACE,OAAO,yHAAY,CACjB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAUD,sBAAY,8CAAkB;aAA9B;YACE,OAAO,UAAC,OAAO;gBACb,cAAO,CAAC,IAAI,CACV,mHAAM,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,EAClC,sEAAQ,CAAC,UAAC,MAA0C;oBAClD,QAAQ,MAAM,CAAC,OAAO,EAAE,CAAC;wBACvB,KAAK,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;wBACvD,KAAK,aAAa,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC3D,KAAK,aAAa,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;wBAC5D,KAAK,aAAa,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;wBACzD,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;oBACrB,CAAC;gBACH,CAAC,CAAC,CACH;YAXD,CAWC,CAAC;QACN,CAAC;;;OAAA;IAUD,sBAAY,8CAAkB;aAA9B;YACE,OAAO,UAAC,OAAO;gBACb,cAAO,CAAC,IAAI,CACV,mHAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,EAC/B,sEAAQ,CAAC,UAAC,EAA2D;wBAAzD,OAAO;oBAAuD;wBACxE,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC;qBACrC;gBAFyE,CAEzE,CAAC,CACH;YALD,CAKC,CAAC;QACN,CAAC;;;OAAA;IAED,sBAAY,gDAAoB;aAAhC;YACE,OAAO,UAAC,OAAO;gBACb,cAAO,CAAC,IAAI,CACV,mHAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,EACnC,iEAAG,CAAC,UAAC,EAA0C;wBAAxC,OAAO;oBACZ,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBAClC,CAAC,CAAC,EAEF,sEAAQ,CAAC,cAAM,SAAE,EAAF,CAAE,CAAC,CACnB;YAPD,CAOC,CAAC;QACN,CAAC;;;OAAA;IAEH,qBAAC;AAAD,CAAC;;;;ACnF6D;AAEzB;AACkC;AAEP;AAEO;AAGrE,oBAAgB,GAOd,OAAO,iBAPO,EAChB,iBAAiB,GAMf,OAAO,kBANQ,EACjB,kBAAkB,GAKhB,OAAO,mBALS,EAClB,mBAAmB,GAIjB,OAAO,oBAJU,EACnB,eAAe,GAGb,OAAO,gBAHM,EACf,4BAAe,GAEb,OAAO,gBAFM,EACf,SAAS,GACP,OAAO,UADA,CACC;AASZ;IAOE,2BAAoB,MAAkB,EAAU,kBAA2B;QAAvD,WAAM,GAAN,MAAM,CAAY;QAAU,uBAAkB,GAAlB,kBAAkB,CAAS;IAAI,CAAC;IAEhF,wCAAY,GAAZ;QACE,OAAO,yHAAY,CACjB,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,sBAAsB,CAC5B,CAAC;IACJ,CAAC;IAED,sBAAY,mDAAoB;aAAhC;YAAA,iBAUC;YATC,OAAO,UAAC,OAAO;gBACb,cAAO,CAAC,IAAI,CACV,mHAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,EACnC,sEAAQ,CAAC;oBAEP,KAAK,CAAC,YAAY,CAAC,KAAI,CAAC,kBAAkB,IAAI,oBAAoB,CAAC,CAAC;oBACpE,OAAO,+DAAK,CAAC;gBACf,CAAC,CAAC,CACH;YAPD,CAOC,CAAC;QACN,CAAC;;;OAAA;IAED,sBAAY,kDAAmB;aAA/B;YAAA,iBAUC;YATC,OAAO,UAAC,OAAO;gBACb,cAAO,CAAC,IAAI,CACV,mHAAM,CAAC,iBAAiB,CAAC,QAAQ,EAAE,EAAE,kBAAkB,CAAC,QAAQ,EAAE,CAAC,EACnE,sEAAQ,CAAC;oBAEP,KAAK,CAAC,YAAY,CAAC,KAAI,CAAC,kBAAkB,IAAI,oBAAoB,CAAC,CAAC;oBACpE,OAAO,+DAAK,CAAC;gBACf,CAAC,CAAC,CACH;YAPD,CAOC,CAAC;QACN,CAAC;;;OAAA;IAWD,sBAAY,qDAAsB;aAAlC;YAAA,iBA4CC;YA3CC,OAAO,UAAC,OAAO;gBACb,cAAO,CAAC,IAAI,CACV,mHAAM,CACJ,OAAO,CAAC,iBAAiB,CAAC,QAAQ,EAAE,EACpC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CACtC,EACD,oEAAM,CAAC,UAAC,EAA2C;wBAAzC,OAAO;oBAAuC,cAAO,CAAC,OAAO,CAAC;gBAAhB,CAAgB,CAAC,EACzE,sEAAQ,CAAC,UAAC,EAA2C;wBAAzC,OAAO;oBACjB,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;wBAE3B,OAAO,gEAAE,CAAC,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;oBAChE,CAAC;oBAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;wBAEjB,OAAO,oEAAM,CACX,gEAAE,CAAC,4BAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,EAC3C,KAAI,CAAC,MAAM,CAAC,MAAM,CAAoB,OAAO,CAAC,CAAC,IAAI,CACjD,sEAAQ,CAAC,UAAC,QAAQ;4BAChB,kCAA2B,CAAC,QAAQ,EAAE;gCACpC,OAAO,CACL,QAAQ,EACR,2CAA2C,EAC3C,KAAK,CACN;oCACC,CAAC,CAAC,SAAS,CACT,OAAO,CACL,QAAQ,EACR,2CAA2C,CAC5C,CACF;oCACD,CAAC,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC;6BACvC,CAAC;wBAbF,CAaE,CACH,CACF,CACF,CAAC;oBACJ,CAAC;oBAGD,OAAO,gEAAE,EAAE,CAAC;gBACd,CAAC,CAAC,EACF,wEAAU,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CACrE;YAzCD,CAyCC,CAAC;QACN,CAAC;;;OAAA;IAEH,wBAAC;AAAD,CAAC;;;;AC1H6D;AACzB;AAEJ;AACY;AAG3C,gBAAY,GAGV,OAAO,aAHG,EACZ,aAAa,GAEX,OAAO,cAFI,EACb,eAAe,GACb,OAAO,gBADM,CACL;AASZ;IAAA;IAiEA,CAAC;IAhEC,iCAAY,GAAZ;QACE,OAAO,yHAAY,CACjB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC;IAWD,sBAAY,0CAAkB;aAA9B;YACE,OAAO,UAAC,OAAO;gBACb,cAAO,CAAC,IAAI,CACV,mHAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,EAC/B,oEAAM,CAAC,UAAC,EAAkD;wBAAhD,OAAO;oBACf,QAAC,KAAK,CAAC,cAAc,CACnB,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAC3D;gBAFD,CAEC,CACF,EACD,iEAAG,CAAC,UAAC,EAA2D;wBAAzD,OAAO;oBACZ,YAAK,CAAC,YAAY,CAChB,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAC3D;gBAFD,CAEC,CACF,EACD,sEAAQ,CAAC,UAAC,EAAkD;wBAAhD,OAAO;oBAA8C;wBAC/D,eAAe,CACb,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CACrD;qBACF;gBAJgE,CAIhE,CAAC,CACH;YAjBD,CAiBC,CAAC;QACN,CAAC;;;OAAA;IAWD,sBAAY,qCAAa;aAAzB;YACE,OAAO,UAAC,OAAO;gBACb,cAAO,CAAC,IAAI,CACV,mHAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,EAChC,oEAAM,CAAC,UAAC,EAAkD;wBAAhD,OAAO;oBACf,YAAK,CAAC,cAAc,CAClB,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAC3D;gBAFD,CAEC,CACF,EACD,iEAAG,CAAC,UAAC,EAAkD;wBAAhD,OAAO;oBACZ,YAAK,CAAC,YAAY,CAChB,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAC3D;gBAFD,CAEC,CACF,CACF;YAZD,CAYC,CAAC;QACN,CAAC;;;OAAA;IAEH,iBAAC;AAAD,CAAC;;;;AC3D2B;AACG;AACN;;;AC1B6B;AAChB;AACD;AACK;AAElC,iCAA6B,GAAK,wEAAc,8BAAnB,CAAoB;AACnD,SAGF,6BAA6B,CAAC,OAAO,CAAC,EAFxC,yBAAe,uBACf,sBAAY,kBAC4B,CAAC;AACpC,SAAS,sBAAsB,CAAC,YAAiB;;IACtD,OAAO;QAGL,YAAY,EAAE,YAAY,CAAC,aAAa,EAAE;QAE1C,YAAY,EAAE,8GAAa;YACzB,GAAC,yBAAe,IAAG,UAAC,KAAK,EAAE,EAAkC;oBAAhC,OAAO;gBAA8B,cAAO;YAAP,CAAO;iBACxE,aAAa,CAAC,QAAQ,CAAC;QAE1B,KAAK,EAAE,8GAAa;YAClB,GAAC,sBAAY,IAAG,UAAC,KAAK,EAAE,EAAwB;oBAAtB,OAAO;gBAAoB,cAAO;YAAP,CAAO;iBAC3D,IAAI,CAAC;KACT,CAAC;AACJ,CAAC;;;ACxB6C;AACR;AACD;AAG7B,8CAA6B,GAAK,wEAAc,8BAAnB,CAAoB;AACnD,qBAIF,0CAA6B,CAAC,OAAO,CAAC,EAHxC,6BAAgB,oCAChB,8BAAiB,qCACjB,+BAAkB,oCACsB,CAAC;AACpC,SAAS,qBAAqB;;IACnC,OAAO;QACL,WAAW,EAAE,8GAAa;YACxB,GAAC,6BAAgB,IAAG,UAAC,KAAK,EAAE,EAAS;oBAAR,OAAO;gBAAM,cAAO,IAAI,KAAK;YAAhB,CAAgB;YAC1D,GAAC,8BAAiB,IAAG,cAAM,WAAI,EAAJ,CAAI;YAC/B,GAAC,+BAAkB,IAAG,cAAM,WAAI,EAAJ,CAAI;iBAC/B,IAAI,CAAQ;KAChB,CAAC;AACJ,CAAC;;;ACnB6C;AACR;AACD;AAE7B,wCAA6B,GAAK,wEAAc,8BAAnB,CAAoB;AAEnD,eAGF,oCAA6B,CAAC,OAAO,CAAC,EAFxC,oBAAa,2BACb,sBAAe,2BACyB,CAAC;AACpC,SAAS,mBAAmB;;IACjC,OAAO;QACL,YAAY,EAAE,8GAAa;YACzB,GAAC,sBAAe,IAAG,UAAC,KAAK,EAAE,EAAW;oBAAT,OAAO;gBAAO,cAAO;YAAP,CAAO;YAClD,GAAC,oBAAa,IAAG,cAAM,WAAI,EAAJ,CAAI;iBAC1B,IAAI,CAAC;KACT,CAAC;AACJ,CAAC;;;AClBoD;AACE;AACR;AAExC,IAAU,QAAQ,CAIxB;AAJD,WAAiB,QAAQ;IACV,4BAAmB,GAAkC,sBAAsB,CAAC;IAC5E,2BAAkB,GAAiC,qBAAqB,CAAC;IACzE,yBAAgB,GAA+B,mBAAmB,CAAC;AAClF,CAAC,EAJgB,QAAQ,KAAR,QAAQ,QAIxB;;;ACR8B;AAWxB,IAAM,cAAc,GAAoB,UAAC,EAE/C;QADC,SAAS,iBAAE,QAAQ;IACf,4FAAK,SAAS,EAAE,qBAAc,SAAS,CAAE,IAAG,QAAQ,CAAO;AAA3D,CAA2D,CAAC;AAElE,cAAc,CAAC,YAAY,GAAG;IAC5B,SAAS,EAAE,EAAE;CACd,CAAC;;;ACjB6B;AAUxB,IAAM,uBAAuB,GAA4B,UAAC,EAEhE;QADC,SAAS,iBAAE,QAAQ;IACf,4FAAK,SAAS,EAAE,yBAAkB,SAAS,CAAE,IAAG,QAAQ,CAAO;AAA/D,CAA+D,CAAC;AAEtE,uBAAuB,CAAC,YAAY,GAAG;IACrC,SAAS,EAAE,EAAE;CACd,CAAC;;;AChB6B;AAWxB,IAAM,kBAAkB,GAAwB,UAAC,EAEvD;QADC,SAAS,iBAAE,QAAQ;IACf,4FAAK,SAAS,EAAE,qCAA8B,SAAS,CAAE,IAAG,QAAQ,CAAO;AAA3E,CAA2E,CAAC;AAElF,kBAAkB,CAAC,YAAY,GAAG;IAChC,SAAS,EAAE,EAAE;CACd,CAAC;AAEsB;AACQ;;;;;;;AEpBD;AAGL;AAEnB,IAAU,QAAQ,CAwKxB;AAxKD,WAAiB,QAAQ;IAmEvB,SAAgB,GAAG,CAAC,SAAiB;QACnC,OAAO;YACL,GAAG,EAAE,SAAS;SACf,CAAC;IACJ,CAAC;IAJe,YAAG,MAIlB;IAuBD,IAAY,YAKX;IALD,WAAY,YAAY;QACtB,kCAAkB;QAClB,iCAAiB;QACjB,6BAAa;QACb,2BAAW;IACb,CAAC,EALW,YAAY,GAAZ,qBAAY,KAAZ,qBAAY,QAKvB;IAED,IAAY,UAIX;IAJD,WAAY,UAAU;QACpB,6BAAe;QACf,4BAAc;QACd,8BAAgB;IAClB,CAAC,EAJW,UAAU,GAAV,mBAAU,KAAV,mBAAU,QAIrB;IAED,IAAY,iBAOX;IAPD,WAAY,iBAAiB;QAC3B,mCAAc;QACd,oCAAe;QACf,gCAAW;QACX,mCAAc;QACd,uCAAkB;QAClB,gCAAW;IACb,CAAC,EAPW,iBAAiB,GAAjB,0BAAiB,KAAjB,0BAAiB,QAO5B;IA2BY,kBAAS,GAAiD,UAAC,KAAK;QAEzE,YAAQ,GAEN,KAAK,SAFC,EACL,QAAQ,UACT,KAAK,EAHH,YAGL,CADY,CACH;QACV,IAAM,SAAS,GAAG,sEAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC;YAAE,MAAM,qDAAqD,CAAC;QACtF,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,0JAE1B,SAAS,CAAC,GAAG,CAAC,UAAC,EAAO,IAAK,iFAAkB,CAAC,EAAE,EAAE,sBAAK,EAAE,CAAC,KAAK,KAAE,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAG,EAApF,CAAoF,CAAC,CAEjH,CAAC,CAAC,CAAC,mGAAiB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAI,CAAC;IAC5D,CAAC,CAAC;IAEF,IAAM,KAAK,GAAG,cAAQ,CAAC,CAAC;IACxB,IAAM,KAAK,GAAG;QACZ,SAAS,EAAE,KAAK;QAChB,aAAa,EAAE,KAAK;QACpB,WAAW,EAAE,KAAK;QAClB,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE,KAAK;QACnB,aAAa,EAAE,KAAK;KACrB,CAAC;IAEF,SAAgB,WAAW;QACzB,OAAO,CAAE,MAAc,CAAC,iBAAiB,CAAC,IAAK,MAAc,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,KAAK,CAAC;IAC3G,CAAC;IAFe,oBAAW,cAE1B;AACH,CAAC,EAxKgB,QAAQ,KAAR,QAAQ,QAwKxB;;;AC7K8B;AACe;AACoB;AACjB;AACP;AACG;AAStC,IAAM,cAAc,GAAoB,UAAC,EAAW;QAAT,OAAO;IACvD,uEAAe,CAAC;QACd,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,QAAQ,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;YAC5B,KAAK,SAAS,CAAC,QAAQ;gBACrB,MAAM,GAAG,GAAG,CAAC;gBACb,MAAM;YACR,KAAK,SAAS,CAAC,EAAE;gBACf,MAAM,GAAG,GAAG,CAAC;gBACb,MAAM;QACV,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAM,GAAG,GAAG,CAAC;QACf,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,KAAK;gBACR,QAAQ,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC;oBAChC,IAAI,EAAE,KAAK,GAAG,OAAO,CAAC,OAAO,EAAE,iBAAiB,EAAE,KAAK,CAAC;oBACxD,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS;oBACnC,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC,OAAO;oBACzC,WAAW,EAAE;wBACX,GAAG,EAAE,uBAAuB;qBAC7B;oBACD,IAAI,EAAE,IAAI;iBACX,EAAE,MAAM,CAAC,CAAC;gBACX,MAAM;YACR,KAAK,QAAQ;gBACX,QAAQ,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC;oBAChC,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS;oBACnC,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC,QAAQ;oBAC1C,WAAW,EAAE;wBACX,GAAG,EAAE,uBAAuB;qBAC7B;iBACF,EAAE,MAAM,CAAC,CAAC;gBACX,MAAM;YACR,KAAK,OAAO,CAAC;YACb;gBACE,QAAQ,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC;oBAChC,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS;oBACnC,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC,QAAQ;oBAC1C,WAAW,EAAE;wBACX,GAAG,EAAE,uBAAuB;qBAC7B;iBACF,EAAE,MAAM,CAAC,CAAC;gBACX,MAAM;QACV,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,OAAO,4EAAC,kBAAkB,IAAC,SAAS,EAAC,wBAAwB;QAC3D,qFAAK,SAAS,EAAC,UAAU,iBAAa,MAAM,GAAG;QAC/C,4EAAC,cAAc,IAAC,SAAS,EAAC,mCAAmC;YAC3D,qFAAK,SAAS,EAAC,KAAK;gBAClB,qFAAK,SAAS,EAAC,wDAAwD;oBACrE,sFAAM,SAAS,EAAC,mCAAmC,GAAG,CAClD;gBACN,qFAAK,SAAS,EAAC,UAAU,iBAAa,MAAM,GAAG;gBAC/C,qFAAK,SAAS,EAAC,oEAAoE;oBACjF,sFAAM,SAAS,EAAC,2BAA2B,EAAC,EAAE,EAAC,uBAAuB;wBACpE,4EAAC,iGAAgB,IAAC,EAAE,EAAC,iBAAiB,GAAG,CACpC,CACH,CACF;YACN,qFAAK,SAAS,EAAC,eAAe,EAAC,KAAK,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE;gBAE/E,CAAC,UAAC,IAAI;oBACJ,QAAQ,IAAI,EAAE,CAAC;wBACb,KAAK,KAAK;4BACR,OAAO,4EAAC,sEAAc;gCACpB,mFAAG,SAAS,EAAC,eAAe;;oCAAqB,OAAO,CAAC,OAAO,EAAE,iBAAiB,EAAE,SAAS,CAAC;;oCAAI,OAAO,CAAC,OAAO,EAAE,qBAAqB,EAAE,SAAS,CAAC;wCAAM;gCAC3J,mFAAG,SAAS,EAAC,eAAe,EAAC,KAAK,EAAE,EAAE,SAAS,EAAE,WAAW,EAAE;;oCAAQ,OAAO,CAAC,OAAO,EAAE,cAAc,EAAE,SAAS,CAAC,CAAK;gCACtH,mFAAG,SAAS,EAAC,eAAe,EAAC,KAAK,EAAE,EAAE,SAAS,EAAE,WAAW,EAAE;;oCAAa,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAK,CACrI,CAAC;wBACpB,KAAK,QAAQ;4BACX,OAAO,4EAAC,sEAAc;gCACpB,mFAAG,SAAS,EAAC,eAAe,2BAAyB;gCACrD,mFAAG,SAAS,EAAC,eAAe;;oCAAY,yFAAM,OAAO,CAAC,cAAc,CAAO,CAAI,CAChE,CAAC;wBACpB,KAAK,OAAO,CAAC;wBACb;4BACE,OAAO,mFAAG,SAAS,EAAC,eAAe,2BAAyB,CAAC;oBACjE,CAAC;gBACH,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;gBAElB,mFAAG,SAAS,EAAC,eAAe;;oBAAc,yFAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAO,CAAI,CAC3G,CACS,CACE,CAAC;AACxB,CAAC,CAAC;;;;;ACxFF,IAAM,gBAAgB,GAA4B,UAAC,KAAK;IACtD,QAAC,OAAO,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpE,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,KAAK,CAAC,WAAW;AAFnB,CAEmB,CAAC;;;;AChBS;AACO;AACE;AAEJ;AACsB;AAG1D,IAAK,WAKJ;AALD,WAAK,WAAW;IACd,qCAAsB;IACtB,uCAAwB;IACxB,qCAAsB;IACtB,yCAA0B;AAC5B,CAAC,EALI,WAAW,KAAX,WAAW,QAKf;AA2BD;IAA+B,6BAAiG;IAAhI;;IA4EA,CAAC;IAvEC,qCAAiB,GAAjB;QAAA,iBAiBC;QAhBC,IAAI,CAAC,KAAK,CAAC,MAAM;YACf,CAAC,CAAC,WAAI,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACvF,IAAI,CAAC,KAAK,CAAC,OAAO;YAChB,CAAC,CAAC,WAAI,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACzF,IAAI,CAAC,KAAK,CAAC,MAAM;YACf,CAAC,CAAC,WAAI,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACvF,IAAI,CAAC,KAAK,CAAC,QAAQ;YACjB,CAAC,CAAC,WAAI,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC3F,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEvC,CAAC,CAAC,WAAI,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE;YAClE,IAAM,OAAO,GAAG,KAAI,CAAC,KAAK,CAAC,YAAY,IAAI,KAAI,CAAC,KAAK,CAAC,YAAY,CAAC,UAAU,IAAI,QAAQ,CAAC,cAAc,CAAC,KAAI,CAAC,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAC7I,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAC3B,KAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;QACjC,CAAC,CACA,CAAC;IACJ,CAAC;IAED,2BAAO,GAAP;QACE,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9D,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAChE,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAED,0BAAM,GAAN;QACQ,SAKF,IAAI,CAAC,KAAK,EAJZ,EAAE,UAAE,iBAAc,EAAd,SAAS,mBAAG,EAAE,OAAE,IAAI,YACxB,KAAK,aAAE,sBAAmB,EAAnB,cAAc,mBAAG,EAAE,OAC1B,OAAO,eAAE,QAAQ,gBAAE,SAAS,iBAC5B,SAAS,eACG,CAAC;QACf,OAAO,qFACL,EAAE,EAAE,EAAE,IAAI,OAAO,EACjB,SAAS,EAAE,8BAAuB,SAAS,CAAE,EAC7C,IAAI,EAAC,QAAQ,EACb,QAAQ,EAAE,CAAC,CAAC,mBACE,QAAQ,mBACR,OAAO,gBACV,MAAM,qBACA,UAAG,EAAE,IAAI,OAAO,WAAQ,iBAE7B,MAAM;YAElB,sFAAM,SAAS,EAAC,SAAS,aAAc;YACvC,qFAAK,SAAS,EAAE,+CAAwC,IAAI,yBAAe,IAAI,CAAE,EAAE,IAAI,EAAC,UAAU;gBAChG,qFAAK,SAAS,EAAC,0CAA0C;oBACvD,qFAAK,SAAS,EAAC,yKAAyK;wBACtL,oFAAI,EAAE,EAAE,UAAG,EAAE,IAAI,OAAO,WAAQ,EAAE,SAAS,EAAC,mHAAmH,IAAE,KAAK,CAAM;wBAC5K,4EAAC,gBAAgB,IAAC,IAAI,EAAE,CAAC,SAAS;4BAChC,wFAAQ,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,gBAAS,EAAE,IAAI,OAAO,CAAE,EAAE,IAAI,EAAC,QAAQ,EAAC,SAAS,EAAC,cAAc,kBAAc,OAAO,gBAAY,cAAc,sBAAmB,UAAG,EAAE,IAAI,OAAO,WAAQ,EAAE,SAAS,EAAE,IAAI;gCAAE,sFAAM,SAAS,EAAC,sBAAsB,GAAQ,CAAS,CACnP,CACf;oBACN,qFAAK,EAAE,EAAE,UAAG,EAAE,IAAI,OAAO,UAAO,EAAE,SAAS,EAAE,2BAAoB,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAE,IACxF,QAAQ,CACL,CACF,CACF,CACF,CAAC;IACT,CAAC;IACD,wCAAoB,GAApB;QACE,IAAI,CAAC,KAAK,CAAC,MAAM;YACf,CAAC,CAAC,WAAI,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACxF,IAAI,CAAC,KAAK,CAAC,OAAO;YAChB,CAAC,CAAC,WAAI,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1F,IAAI,CAAC,KAAK,CAAC,MAAM;YACf,CAAC,CAAC,WAAI,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACxF,IAAI,CAAC,KAAK,CAAC,QAAQ;YACjB,CAAC,CAAC,WAAI,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC9F,CAAC;IA1EM,sBAAY,GAAG;QACpB,SAAS,EAAE,EAAE;QACb,IAAI,EAAE,IAAI;KACX,CAAC;IAwEJ,gBAAC;CAAA,CA5E8B,uEAAe,GA4E7C;AA5EqB;AA8Ef,IAAM,iBAAiB,GAAG,gGAAO,CACtC,UAAC,EAAqB;QAAnB,YAAY;IAAY,QAAC,EAAE,YAAY,gBAAE,CAAC;AAAlB,CAAkB,EAC7C,UAAC,QAAQ,IAAK,QAAC;IACb,eAAe,EAAE,UAAC,OAAe,IAAK,eAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAxC,CAAwC;IAC9E,iBAAiB,EAAE,cAAM,eAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,EAArC,CAAqC;CAC/D,CAAC,EAHY,CAGZ,CACH,CAAC,SAAS,CAAC,CAAC;;;;AC1HkB;AACO;AACE;AACqB;AAEnB;AACJ;AACU;AACU;AAe1D,SAAS,cAAc,CAAC,CAAS;IAC/B,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,yBAAyB,CAAC;QAC/C,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,yBAAyB,CAAC;QAC/C,OAAO,CAAC,CAAC,OAAO,cAAc,CAAC;IACjC,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CAAC,IAAY;IAClC,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,OAAO,CAAC,CAAC,OAAO,sFAAM,SAAS,EAAC,wDAAwD,GAAG,CAAC;QACjG,KAAK,aAAa,CAAC,CAAC,OAAO,sFAAM,SAAS,EAAC,2DAA2D,GAAG,CAAC;QAC1G,KAAK,SAAS,CAAC,CAAC,OAAO,sFAAM,SAAS,EAAC,2DAA2D,GAAG,CAAC;QACtG,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;IACvB,CAAC;AACH,CAAC;AAED,IAAI,YAAY,GAAW,EAAE,CAAC;AAC9B,SAAS,cAAc,CAAC,EAAU;IAChC,IAAI,WAAW,CAAC;IAChB,IAAM,MAAM,GAAG;QACb,QAAQ,EAAE,GAAG;QACb,YAAY,EAAE,CAAC;QACf,gBAAgB,EAAE,CAAC;KACpB,CAAC;IACF,QAAQ,YAAY,EAAE,CAAC;QACrB,KAAK,SAAS,CAAC;QACf,KAAK,OAAO;YACV,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;YACxB,MAAM,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAC5B,WAAW,GAAG,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC;YAC5C,MAAM;QACR,KAAK,aAAa,CAAC;QACnB;YACE,WAAW,GAAG,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC;IACpD,CAAC;IACD,QAAQ,CAAC,WAAW,EAAE,CAAC,aAAa,CAAC;QACnC,EAAE,EAAE,qBAAqB;QACzB,MAAM;QACN,MAAM,EAAE;YACN,GAAG,EAAE,UAAG,EAAE,WAAQ;SACnB;QACD,MAAM,EAAE;YACN,GAAG,EAAE,UAAG,EAAE,iBAAc;SACzB;QACD,MAAM,EAAE;YACN,OAAO,EAAE;gBACP,GAAG,EAAE,UAAG,EAAE,iBAAc;aACzB;YACD,IAAI,EAAE,WAAW;SAClB;KACF,CAAC,CAAC;AACL,CAAC;AAAA,CAAC;AAEF,IAAM,qBAAS,GAAgF,UAAC,EAW/F;QAVC,EAAE,UACF,IAAI,YACJ,KAAK,aACL,WAAW,mBACX,WAAW,mBACX,iBAAiB,yBACjB,WAAW,mBACX,SAAS,iBACT,QAAQ,gBACR,UAAU;IAEV,YAAY,GAAG,IAAI,CAAC;IACpB,OAAO,4EAAC,iBAAiB,IAAC,OAAO,EAAE,EAAE,IAAI,oBAAoB,EAAE,SAAS,EAAE,IAAI,EAC5E,OAAO,EAAE,cAAQ,SAAS,EAAE,CAAC,CAAC,UAAU,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAClE,OAAO,EAAE,cAAM,qBAAc,CAAC,EAAY,CAAC,EAA5B,CAA4B,EAC3C,KAAK,EAAE,KAAK;QACZ,qFAAK,SAAS,EAAC,oBAAoB;YACjC,qFAAK,SAAS,EAAC,MAAM;gBAClB,cAAc,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC3B,qFAAK,EAAE,EAAE,UAAG,EAAE,iBAAc;oBAC1B,mFAAG,uBAAuB,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,GAAI;oBACvD,4EAAC,gBAAgB,IAAC,IAAI,EAAE,OAAO,CAAC,WAAW,EAAE,oBAAoB,EAAE,KAAK,CAAC;wBACvE,wFAEI,OAAO,CAAC,WAAW,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,UAAC,OAAe,IAAK,2FAAI,SAAS,EAAC,kBAAkB,IAAE,OAAO,CAAM,EAA/C,CAA+C,CAAC,CAEhH,CACY;oBACnB,4EAAC,gBAAgB,IAAC,IAAI,EAAE,OAAO,CAAC,WAAW,EAAE,kBAAkB,EAAE,KAAK,CAAC;wBACrE,wFAEI,OAAO,CAAC,WAAW,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,UAAC,KAAU,IAAK;4BACxD,sFAAM,SAAS,EAAC,kBAAkB,IAAE,KAAK,CAAC,SAAS,CAAQ;4BAAA,uFAAM;4BACjE,4EAAC,gBAAgB,IAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;gCAChD,4EAAC,8FAAa,IAAC,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,MAAM,EAAC,OAAO,EAAC,QAAQ,EAAC,KAAK,IACnE,UAAC,UAAe,IAAK,mFAAC,iGAAgB,IAAC,EAAE,EAAC,kBAAkB,EAAC,MAAM,EAAE,EAAE,UAAU,cAAE,GAAI,EAAlE,CAAkE,CAC1E,CACC,CAChB,EAPqD,CAOrD,CAAC,CAEL,CACY;oBACnB,4EAAC,gBAAgB,IAAC,IAAI,EAAE,OAAO,CAAC,iBAAiB,CAAC;wBAChD,mFAAG,SAAS,EAAC,SAAS,EAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAI,CAChE,CACf,CACF,CACF;QACN,4EAAC,gBAAgB,IAAC,IAAI,EAAE,OAAO,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;YACpE,qFAAK,SAAS,EAAC,sBAAsB,iBAAa,MAAM,GAAO;YAC/D,qFAAK,SAAS,EAAC,sCAAsC,IAEjD,OAAO,CAAgC,WAAW,EAAE,SAAS,EAAE,EAAE,CAAC;iBAC/D,GAAG,CAAC,UAAC,MAAM,EAAE,CAAC,IAAK,mFAAC,sEAAc;gBACjC,wFAAQ,EAAE,EAAE,eAAe,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,cAAQ,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAG,MAAM,CAAC,IAAI,CAAU;gBAC7J,qFAAK,SAAS,EAAC,WAAW,iBAAa,MAAM,GAAO,CACrC,EAHG,CAGH,CAAC,CAElB,CACW,CACD,CAAC;AACvB,CAAC,CAAC;AAEK,IAAM,oBAAoB,GAAG,gGAAO,CACzC,UAAC,EAAoB;QAAlB,WAAW;IAAY,QAAC,WAAW,CAAC,CAAC,CAAC,aAAK,WAAW,EAAG,CAAC,CAAC,EAAE,CAA6B;AAAnE,CAAmE,EAC7F,UAAC,QAAQ,IAAK,QAAC;IACb,QAAQ,EAAE,UAAC,MAAM;QACf,QAAQ,CAAC,WAAW,EAAE,CAAC,WAAW,CAAC;YACjC,EAAE,EAAE,qBAAqB;YACzB,MAAM,EAAE;gBACN,QAAQ,EAAE,GAAG;gBACb,YAAY,EAAE,CAAC;gBACf,gBAAgB,EAAE,CAAC;aACpB;YACD,MAAM,EAAE,MAAM,CAAC,IAAI;SACpB,CAAC,CAAC;QACH,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,KAAK,QAAQ;gBAAE,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;gBAAC,MAAM;YACnE,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IACD,SAAS,EAAE,cAAM,eAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,EAAtC,CAAsC;CACxD,CAAC,EAjBY,CAiBZ,CACH,CAAC,qBAAS,CAAC,CAAC;;;ACnKkB;AAUxB,IAAM,YAAY,GAAkB,UAAC,KAAU;IAC5C,QAAI,GAA2B,KAAK,KAAhC,EAAE,SAAS,GAAgB,KAAK,UAArB,EAAE,SAAS,GAAK,KAAK,UAAV,CAAW;IAE7C,OAAO,mFAAG,SAAS,EAAE,wBAAiB,SAAS,CAAE,IAAG,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,QAAK,CAAK,CAAC;AAClI,CAAC,CAAC;AAEF,YAAY,CAAC,YAAY,GAAG;IAC1B,SAAS,EAAE,EAAE;CACd,CAAC;AAEF,YAAY,CAAC,WAAW,GAAG,cAAc,CAAC;;;;ACpBX;AACO;AACI;AACW;AACX;AAiB1C,IAAM,qBAAS,GAA2D,UAAC,KAAK,IAAK,QACnF,OAAO,CAAC,KAAK,CAAC,YAAY;IACpB,KAAK,CAAC,YAAY,CAAC,QAAQ;IAC3B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IACtD,4EAAC,6FAAY,eAAK,KAAK,CAAC,YAAY,IAAE,OAAO,wBAAO,KAAK,CAAC,YAAY,CAAC,OAAO,KAAE,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,qBAAqB,EAAE,CAAC,EAAE,EAAE,KAAI,MAAM,EAAE,KAAK,CAAC,YAAY,CAAC,UAAU;QACnO,4EAAC,sEAAc,QACZ,CAAC,UAAC,MAAqB;YACtB,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,aAAa,CAAC,QAAQ,CAAC;gBAC5B,KAAK,aAAa,CAAC,QAAQ;oBACzB,OAAO,KAAK,CAAC,QAAQ,CAAC;gBACxB,KAAK,aAAa,CAAC,KAAK;oBACtB,OAAO,4EAAC,cAAc,IAAC,OAAO,EAAE,KAAK,CAAC,iBAAiB,GAAI,CAAC;gBAC9D;oBACE,OAAO,KAAK,CAAC,WAAW,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CACP,CACJ,CAAC,CAAC,CAAC,IAAI,CAAC,EAlB0D,CAkB1D,CAAC;AAGrB,IAAM,wBAAwB,GAAG,gGAAO,CAC7C,UAAC,EAA6D;QAA3D,YAAY,oBAAE,KAAK,aAAE,YAAY;IAA+B,QAAC;QAClE,YAAY;QACZ,YAAY;QACZ,iBAAiB,EAAE,KAAK;KACzB,CAAC;AAJiE,CAIjE,EAAE,EAAE,EACN,UAAU,UAAU,EAAE,aAAa,EAAE,QAAQ;IAC3C,OAAO,+BACF,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GACtE,aAAa,GACb,QAAQ,EACX;AACJ,CAAC,CACF,CAAC,qBAAS,CAAC,CAAC;;;;ACvDkB;AAG/B,SAAS,eAAe,CAAC,KAAa;IACpC,IAAI,CAAC,KAAK;QAAE,OAAO,IAAI,CAAC;IACxB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,UAAG,KAAK,MAAG,CAAC;IAC3C,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3B,CAAC;AAEM,SAAS,eAAe,CAAC,YAA+B,EAAE,KAAU;IACzE,IAAI,CAAC;QACH,IAAM,CAAC,GAAkB,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,IAAM,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEnB,IAAM,MAAM,GAAY,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACjD,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAEnC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAE7D,CAAC;QAED,QAAQ,YAAY,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ;YACR,KAAK,IAAI;gBACP,IAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBACzD,IAAI,MAAM,EAAE,CAAC;oBACX,OAAO,cAAO,SAAS,cAAI,eAAe,CAAC,KAAK,CAAC,CAAE,CAAC;gBACtD,CAAC;qBAAM,CAAC;oBACN,OAAO,WAAI,SAAS,cAAI,eAAe,CAAC,KAAK,CAAC,CAAE,CAAC;gBAEnD,CAAC;YACH,KAAK,IAAI;gBACP,IAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBACzD,IAAI,MAAM,EAAE,CAAC;oBACX,OAAO,aAAM,SAAS,cAAI,eAAe,CAAC,KAAK,CAAC,YAAS,CAAC;gBAC5D,CAAC;qBAAM,CAAC;oBACN,OAAO,UAAG,SAAS,cAAI,eAAe,CAAC,KAAK,CAAC,YAAS,CAAC;gBACzD,CAAC;QACL,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QAEX,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAWD,IAAM,UAAU,GAAG,UAAC,EAA4E;QAA1E,KAAK,aAAE,SAAS,iBAAE,YAAY,oBAAE,GAAG,WAAE,QAAQ,gBAAE,MAAM;IACzE,IAAM,SAAS,GAAG,GAAG,IAAI,MAAM,CAAC;IAChC,OAAO,4EAAC,SAAS,eAAK,QAAQ,IAAE,SAAS,EAAE,sBAAe,SAAS,IAAI,EAAE,CAAE,EAAE,uBAAuB,EAAE,EAAE,MAAM,EAAE,eAAe,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;AAC7J,CAAC,CAAC;AAEF,mDAAe,UAAU,EAAC;;;AC3DY;AACE;AAejC,IAAM,qBAAqB,GAAI,gGAAO,CAE3C,UAAC,EAAqB;QAAnB,YAAY;IAAY,QAAC,EAAE,YAAY,gBAAE,CAAC;AAAlB,CAAkB,EAE7C,UAAC,QAAQ,IAAK,QAAC,EAAE,CAAC,EAAJ,CAAI,CACnB,CAAC,YAAiB,CAAkD,CAAC;AACtE,qBAAqB,CAAC,WAAW,GAAG,cAAc,CAAC;;;;ACvBpB;AACe;AACH;AAGpC,IAAM,uBAAuB,GAAkB,UACpD,EAAqB;IAAnB,MAAE,UAAK,KAAK,cAAd,MAAgB,CAAF;IACX,QAAC,4EAAC,OAAO,QAAE,UAAC,EAAU;YAAR,MAAM;QAAO,mFAAC,iGAAgB,eAAK,KAAK,IAAE,EAAE,EAAE,UAAG,MAAM,CAAC,oBAAoB,CAAC,KAAK,cAAI,EAAE,CAAE,IAAI;IAAjF,CAAiF,CAAW,CAAC;CAAA,CAAC;;;ACP/F;AACgC;AAc/D,IAAM,QAAQ,GAAkB,UAAC,EAIhC;QAHC,GAAG,WACH,eAAe,uBACf,iBAAiB;IAEjB,IAAI,KAAK,GAAW,EAAE,EAAE,QAAQ,GAAW,EAAE,EAAE,MAAM,GAAW,EAAE,CAAC;IACnE,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QAC3B,IAAM,KAAK,GAAkB,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5C,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC3B,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;SAAM,CAAC;QACN,IAAM,KAAK,GAAkB,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5C,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IACD,OAAO;QACJ,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,qFAAK,SAAS,EAAE,eAAe,IAAG,MAAM,CAAO,CAAC,CAAC,CAAC,IAAI;QACxE,KAAK;QACN,qFAAK,SAAS,EAAE,iBAAiB,yBAAe,QAAQ,CAAO;QAC9D,QAAQ,KAAK,IAAI,IAAI,sFAAM,SAAS,EAAC,SAAS;;YAAG,QAAQ;qBAAc,CACvE,CAAC;AACN,CAAC,CAAC;AAEK,IAAM,iBAAiB,GAAsB,UAAC,EAMpD;QALC,SAAS,iBACT,eAAe,uBACf,iBAAiB,yBACjB,KAAK,aACL,OAAO;IACH,mFAAC,gGAAe,IAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAC,KAAK,IAE7C,UAAC,GAAW,IAAK,6FAAM,SAAS,EAAE,6BAAsB,SAAS,CAAE;QACjE,4EAAC,QAAQ,IAAC,GAAG,EAAE,GAAG,EAChB,eAAe,EAAE,eAAe,EAChC,iBAAiB,EAAE,iBAAiB,GAAI;QACzC,OAAO,CAAC,CAAC;YACR,qFAAK,SAAS,EAAE,eAAe;gBAC7B,4EAAC,iGAAgB,IAAC,EAAE,EAAC,QAAQ,IAAE,UAAC,MAAM,IAAK,4GAAkB,MAAM,IAAE,MAAM,CAAQ,EAAxC,CAAwC,CAAoB;gBACvG,4EAAC,iGAAgB,IAAC,EAAE,EAAC,WAAW,IAAE,UAAC,SAAS,IAAK,6FAAM,SAAS,EAAC,SAAS,IAAE,SAAS,CAAQ,EAA5C,CAA4C,CAAoB,CAC7G,CAAC,CAAC,CAAC,IAAI,CACV,EATU,CASV,CAEO;AAbZ,CAaY,CAAC;AAEnB,iBAAiB,CAAC,YAAY,GAAG;IAC/B,SAAS,EAAE,EAAE;IACb,eAAe,EAAE,WAAW;IAC5B,iBAAiB,EAAE,WAAW;CAC/B,CAAC;;;AC/D6B;AACI;AACR;;;ACF3B,SAAS,YAAO,QAAQ,2EAA2E,YAAO,2BAA2B,wBAAwB,OAAO,YAAO,2BAA2B,mIAAmI,OAAO,YAAO;;AAEvV,kDAAkD,0CAA0C;;AAE5F,4CAA4C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD;;AAE/P,8DAA8D,sEAAsE,8DAA8D;;AAElM,kDAAkD,aAAa,YAAO,sDAAsD,eAAe;;AAE3I,8BAA8B,gGAAgG,mDAAmD;;AAEjL,wCAAwC,uBAAuB,yFAAyF;;AAExJ,2CAA2C,+DAA+D,6EAA6E,yEAAyE,eAAe,uDAAuD,GAAG;;AAEzU,iCAAiC,4EAA4E,iBAAiB,aAAa;;AAE3I,SAAS,oBAAe,oBAAoB,kBAAkB,kCAAkC,oEAAoE,KAAK,OAAO,oBAAoB;;AAEvJ,CAAC;;AAEvC;AACP;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA,wEAAwE,aAAa;AACrF;AACA;;AAEA;;AAEA,IAAI,oBAAe;AACnB;AACA,KAAK;;AAEL,IAAI,oBAAe;;AAEnB,IAAI,oBAAe;AACnB;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX,UAAU;AACV;AACA;AACA,WAAW;AACX;;AAEA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,UAAU,IAAqC;AAC/C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA,CAAC,CAAC,2EAAa;;AAEf,oBAAe;AACf;AACA;AACA,CAAC,E;;ACxG8B;AACc;AACiB;AAwBvD,IAAM,gBAAgB,GAAqB,UAAC,KAAa,IAAK,QACnE,4EAAC,WAAW,IAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,IAC7D,KAAK,CAAC,MAAM,EAAE,CACH,CACf,EAJoE,CAIpE,CAAC;;;AC7BwD;AAC+F;AAC1G;AACM;AACiC;AACN;AACuE;AACjG;AACG;AAElD,IAAU,UAAU,CAc1B;AAdD,WAAiB,UAAU;IACZ,gBAAK,GAAoB,cAAc,CAAC;IACxC,oBAAS,GAAwB,kBAAkB,CAAC;IACpD,gBAAK,GAAoB,cAAc,CAAC;IACxC,wBAAa,GAA4B,uBAAuB,CAAC;IACjE,gBAAK,GAA6B,iBAAiB,CAAC;IACpD,2BAAgB,GAAgC,oBAAoB,CAAC;IACrE,0BAAe,GAAoC,wBAA+B,CAAC;IACnF,uBAAY,GAAkB,YAAqB,CAAC;IACpD,mBAAQ,GAAqC,iBAAiB,CAAC;IAC/D,uBAAY,GAAyC,qBAAqB,CAAC;IAC3E,yBAAc,GAAkB,uBAAuB,CAAC;IACxD,sBAAW,GAA4B,gBAAgB,CAAC;IACxD,kBAAO,GAA4B,gBAAgB,CAAC;AACnE,CAAC,EAdgB,UAAU,KAAV,UAAU,QAc1B;;;ACzByB;AACD;AACC;AACF;AACC;AACE;AACH;AACS;AACN", "sources": ["[name]:///webpack/universalModuleDefinition?", "[name]:///./redux-persist/lib/stateReconciler/hardSet.js?", "[name]:///./redux-persist/lib/storage/createWebStorage.js?", "[name]:///./redux-persist/lib/storage/getStorage.js?", "[name]:///./redux-persist/lib/storage/session.js?", "[name]:///external umd {\"root\":\"bwtk\",\"commonjs2\":\"bwtk\",\"commonjs\":\"bwtk\",\"amd\":\"bwtk\"}?", "[name]:///external umd {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}?", "[name]:///external umd {\"root\":\"ReactIntl\",\"commonjs2\":\"react-intl\",\"commonjs\":\"react-intl\",\"amd\":\"react-intl\"}?", "[name]:///external umd {\"root\":\"ReactRedux\",\"commonjs2\":\"react-redux\",\"commonjs\":\"react-redux\",\"amd\":\"react-redux\"}?", "[name]:///external umd {\"root\":\"Redux\",\"commonjs2\":\"redux\",\"commonjs\":\"redux\",\"amd\":\"redux\"}?", "[name]:///external umd {\"root\":\"ReduxActions\",\"commonjs2\":\"redux-actions\",\"commonjs\":\"redux-actions\",\"amd\":\"redux-actions\"}?", "[name]:///external umd {\"root\":\"ReduxObservable\",\"commonjs2\":\"redux-observable\",\"commonjs\":\"redux-observable\",\"amd\":\"redux-observable\"}?", "[name]:///external umd {\"root\":\"rxjs\",\"commonjs2\":\"rxjs\",\"commonjs\":\"rxjs\",\"amd\":\"rxjs\"}?", "[name]:///webpack/bootstrap?", "[name]:///webpack/runtime/compat get default export?", "[name]:///webpack/runtime/define property getters?", "[name]:///webpack/runtime/hasOwnProperty shorthand?", "[name]:///webpack/runtime/make namespace object?", "[name]:///../src/Actions/index.ts?", "[name]:///./tslib/tslib.es6.mjs?", "[name]:///./redux-persist/es/constants.js?", "[name]:///./redux-persist/es/stateReconciler/autoMergeLevel1.js?", "[name]:///./redux-persist/es/createPersistoid.js?", "[name]:///./redux-persist/es/getStoredState.js?", "[name]:///./redux-persist/es/purgeStoredState.js?", "[name]:///./redux-persist/es/persistReducer.js?", "[name]:///./redux-persist/es/stateReconciler/autoMergeLevel2.js?", "[name]:///./redux-persist/es/persistCombineReducers.js?", "[name]:///./redux-persist/es/persistStore.js?", "[name]:///./redux-persist/es/createMigrate.js?", "[name]:///./redux-persist/es/createTransform.js?", "[name]:///./redux-persist/es/index.js?", "[name]:///../src/Utils/ExtractProp.ts?", "[name]:///../src/Utils/Assert.ts?", "[name]:///../src/Utils/FilterRestrictionObservable.ts?", "[name]:///../src/Utils/FormattedHTMLMessage.tsx?", "[name]:///../src/Utils/index.ts?", "[name]:///../src/Models/VOLT.ts?", "[name]:///../src/Models/index.ts?", "[name]:///../src/Client/index.ts?", "[name]:///../src/Context/index.tsx?", "[name]:///../src/Epics/Lifecycle.ts?", "[name]:///../src/Epics/Restrictions.ts?", "[name]:///../src/Epics/Modals.ts?", "[name]:///../src/Epics/index.ts?", "[name]:///../src/Reducers/Lifecycle.ts?", "[name]:///../src/Reducers/Restrictions.ts?", "[name]:///../src/Reducers/Modals.ts?", "[name]:///../src/Reducers/index.ts?", "[name]:///../src/ViewComponents/Container/Panel.tsx?", "[name]:///../src/ViewComponents/Container/BRF3Container.tsx?", "[name]:///../src/ViewComponents/Container/index.tsx?", "[name]:///../src/Omniture/Tracker.ts?", "[name]:///../src/Omniture/index.tsx?", "[name]:///../src/ViewComponents/Error/index.tsx?", "[name]:///../src/ViewComponents/VisibilityContainer/index.tsx?", "[name]:///../src/ViewComponents/Lightbox/index.tsx?", "[name]:///../src/ViewComponents/Restriction/index.tsx?", "[name]:///../src/ViewComponents/EllipsisText/index.tsx?", "[name]:///../src/ViewComponents/Application/index.tsx?", "[name]:///../src/ViewComponents/Localization/BellCurrency/BellCurrency.tsx?", "[name]:///../src/ViewComponents/Localization/BellCurrency/index.ts?", "[name]:///../src/ViewComponents/Localization/FormattedMessage/index.tsx?", "[name]:///../src/ViewComponents/Localization/Currency/index.tsx?", "[name]:///../src/ViewComponents/Localization/index.ts?", "[name]:///./redux-persist/es/integration/react.js?", "[name]:///../src/ViewComponents/ReduxPersistGate/index.tsx?", "[name]:///../src/ViewComponents/index.ts?", "[name]:///../src/index.ts?"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"bwtk\"), require(\"redux-actions\"), require(\"rxjs\"), require(\"redux\"), require(\"react\"), require(\"react-intl\"), require(\"redux-observable\"), require(\"react-redux\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"omf-changepackage-components\", [\"bwtk\", \"redux-actions\", \"rxjs\", \"redux\", \"react\", \"react-intl\", \"redux-observable\", \"react-redux\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"omf-changepackage-components\"] = factory(require(\"bwtk\"), require(\"redux-actions\"), require(\"rxjs\"), require(\"redux\"), require(\"react\"), require(\"react-intl\"), require(\"redux-observable\"), require(\"react-redux\"));\n\telse\n\t\troot[\"omf-changepackage-components\"] = factory(root[\"bwtk\"], root[\"ReduxActions\"], root[\"rxjs\"], root[\"Redux\"], root[\"React\"], root[\"ReactIntl\"], root[\"ReduxObservable\"], root[\"ReactRedux\"]);\n})(self, (__WEBPACK_EXTERNAL_MODULE_bwtk__, __WEBPACK_EXTERNAL_MODULE_redux_actions__, __WEBPACK_EXTERNAL_MODULE_rxjs__, __WEBPACK_EXTERNAL_MODULE_redux__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_intl__, __WEBPACK_EXTERNAL_MODULE_redux_observable__, __WEBPACK_EXTERNAL_MODULE_react_redux__) => {\nreturn ", "\"use strict\";\n\nexports.__esModule = true;\nexports.default = hardSet;\n\n/*\n  hardSet: \n    - hard set incoming state\n*/\nfunction hardSet(inboundState) {\n  return inboundState;\n}", "\"use strict\";\n\nexports.__esModule = true;\nexports.default = createWebStorage;\n\nvar _getStorage = _interopRequireDefault(require(\"./getStorage\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction createWebStorage(type) {\n  var storage = (0, _getStorage.default)(type);\n  return {\n    getItem: function getItem(key) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.getItem(key));\n      });\n    },\n    setItem: function setItem(key, item) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.setItem(key, item));\n      });\n    },\n    removeItem: function removeItem(key) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.removeItem(key));\n      });\n    }\n  };\n}", "\"use strict\";\n\nexports.__esModule = true;\nexports.default = getStorage;\n\nfunction _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction noop() {}\n\nvar noopStorage = {\n  getItem: noop,\n  setItem: noop,\n  removeItem: noop\n};\n\nfunction hasStorage(storageType) {\n  if ((typeof self === \"undefined\" ? \"undefined\" : _typeof(self)) !== 'object' || !(storageType in self)) {\n    return false;\n  }\n\n  try {\n    var storage = self[storageType];\n    var testKey = \"redux-persist \".concat(storageType, \" test\");\n    storage.setItem(testKey, 'test');\n    storage.getItem(testKey);\n    storage.removeItem(testKey);\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') console.warn(\"redux-persist \".concat(storageType, \" test failed, persistence will be disabled.\"));\n    return false;\n  }\n\n  return true;\n}\n\nfunction getStorage(type) {\n  var storageType = \"\".concat(type, \"Storage\");\n  if (hasStorage(storageType)) return self[storageType];else {\n    if (process.env.NODE_ENV !== 'production') {\n      console.error(\"redux-persist failed to create sync storage. falling back to noop storage.\");\n    }\n\n    return noopStorage;\n  }\n}", "\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n\nvar _createWebStorage = _interopRequireDefault(require(\"./createWebStorage\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar _default = (0, _createWebStorage.default)('session');\n\nexports.default = _default;", "module.exports = __WEBPACK_EXTERNAL_MODULE_bwtk__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_react__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_react_intl__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_react_redux__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_redux__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_redux_actions__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_redux_observable__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_rxjs__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { CommonServices, ServiceLocator } from \"bwtk\";\r\nimport { Action, createAction } from \"redux-actions\";\r\nimport { EWidgetRoute, EWidgetStatus, ILightboxPayload, Models, Volt } from \"../Models\";\r\n\r\nexport namespace Actions {\r\n  /* Widget Life cycle Events */\r\n  export const setWidgetStatus = createAction<EWidgetStatus>(\"SET_WIDGET_STATUS\") as (payload: EWidgetStatus) => Action<EWidgetStatus>;\r\n  export const setWidgetProps = createAction<Models.IBaseWidgetProps | any>(\"SET_WIDGET_PROPS\") as (payload: Models.IBaseWidgetProps | any) => Action<Models.IBaseWidgetProps | any>;\r\n  export const getData = createAction(\"GET_WIDGET_DATA\");\r\n\r\n  // Loader Life cycle events\r\n  export const showHideLoader = createAction<boolean | null>(\"SHOW_HIDE_LOADER\") as (payload: boolean | null) => Action<boolean>;\r\n  // Widget Error handeling\r\n  export const errorOccured = createAction<Models.IErrorHandlerProps>(\"ERROR_OCCURED\") as (error: Models.IErrorHandlerProps) => Action<Models.IErrorHandlerProps>;\r\n\r\n  // Lightboxes/modals\r\n  export const openLightbox = createAction<string | ILightboxPayload>(\"OPEN_LIGHTBOX\") as (lightboxId: string | ILightboxPayload) => ReduxActions.Action<string | ILightboxPayload>;\r\n  export const closeLightbox = createAction<string>(\"CLOSE_LIGHTBOX\") as (lightboxId: string) => ReduxActions.Action<string>;\r\n  export const setlightboxData = createAction<any>(\"SET_LIGHTBOX_DATA\") as (payload: any) => ReduxActions.Action<any>;\r\n\r\n  // Piped actions\r\n  export const broadcastUpdate = createAction<Action<any>>(\"PIPE_SEND_UPDATE\", ((action: Action<any>, delay: number = 0) => { setTimeout(() => ServiceLocator.instance.getService(CommonServices.EventStream).send(action.type, action.payload), delay); return action; }) as any) as (action: Action<any>, delay?: number) => ReduxActions.Action<Action<any>>;\r\n  export const refreshTotals = createAction(\"REFRESH_TOTALS\");\r\n  export const toggleTVCategoriesTray = createAction(\"TOGGLE_TV_CATEGORIES_TRAY\");\r\n  export const setProductConfigurationTotal = createAction<Volt.IProductConfigurationTotal>(\"SET_PRODUCT_CONFIGURATION_TOTAL\") as (totals: Volt.IProductConfigurationTotal) => ReduxActions.Action<Volt.IProductConfigurationTotal>;\r\n  export const continueFlow = createAction<EWidgetRoute>(\"FLOW_CONTINUE\") as (route: EWidgetRoute) => Action<EWidgetRoute>;\r\n  export const handleNav = createAction<boolean>(\"HANDLE_NAV\") as (payload: boolean) => Action<boolean>;\r\n\r\n  // Browser routes and history events\r\n  export const onContinue = createAction(\"HISTORY_ON_CONTINUE\");\r\n  export const historyGo = createAction<string>(\"HISTORY_GO\") as (payload: string) => ReduxActions.Action<string>;\r\n  export const historyBack = createAction<any>(\"HISTORY_BACK\") as (payload?: any) => ReduxActions.Action<any>;\r\n  export const historyForward = createAction(\"HISTORY_FORWARD\");\r\n  export const applicationReset = createAction(\"APPLICATION_RESET\");\r\n  export const applicationExit = createAction(\"APPLICATION_EXIT\");\r\n  export const applicationLogout = createAction(\"APPLICATION_LOGOUT\");\r\n  export const setHistoryProvider = createAction<any>(\"SET_HISTORY_PROVIDER\") as (payload: any) => ReduxActions.Action<any>;\r\n  export const setAppointmentVisited = createAction(\"APPOINTMENT_PAGE_VISITED\");\r\n  // Third Party Integration\r\n  export const widgetRenderComplete = createAction(\"WIDGET_RENDER_COMPLETE\");\r\n  // Restrictions\r\n  export const raiseRestriction = createAction<Volt.IRestriction>(\"RESTRICTION_OCCURRED\") as (message: Volt.IRestriction, onComplete?: any) => ReduxActions.Action<Volt.IRestriction>;\r\n  export const acceptRestriction = createAction<Volt.IHypermediaAction>(\"RESTRICTION_ACCEPTED\") as (action: Volt.IHypermediaAction) => ReduxActions.Action<Volt.IHypermediaAction>;\r\n  export const declineRestriction = createAction<Volt.IHypermediaAction>(\"RESTRICTION_DECLINED\") as (action?: Volt.IHypermediaAction) => ReduxActions.Action<Volt.IHypermediaAction>;\r\n  export const finalizeRestriction = createAction<any>(\"RESTRICTION_CYCLE_COMPLETE\") as (data?: any) => ReduxActions.Action<any>;\r\n  export const clearCachedState = createAction<string[]>(\"CLEAR_CACHED_STATE\") as (payload: string[]) => ReduxActions.Action<string[]>;\r\n  // Omniture\r\n  export const omniPageLoaded = createAction<{ name: string, data?: any }>(\"OMNITURE_PAGE_LOADED\", ((name?: string, data?: any) => (name ? { name, data } : undefined)) as any) as (name?: string, data?: any) => ReduxActions.Action<{ name: string, data?: any }>;\r\n  export const omniPageSubmit = createAction<string>(\"OMNITURE_PAGE_SUBMIT\") as (payload?: string) => ReduxActions.Action<string>;\r\n  export const omniModalOpen = createAction<string>(\"OMNITURE_MODAL_OPEN\") as (payload?: string) => ReduxActions.Action<string>;\r\n}\r\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "export var KEY_PREFIX = 'persist:';\nexport var FLUSH = 'persist/FLUSH';\nexport var REHYDRATE = 'persist/REHYDRATE';\nexport var PAUSE = 'persist/PAUSE';\nexport var PERSIST = 'persist/PERSIST';\nexport var PURGE = 'persist/PURGE';\nexport var REGISTER = 'persist/REGISTER';\nexport var DEFAULT_VERSION = -1;", "function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n/*\n  autoMergeLevel1: \n    - merges 1 level of substate\n    - skips substate if already modified\n*/\nexport default function autoMergeLevel1(inboundState, originalState, reducedState, _ref) {\n  var debug = _ref.debug;\n\n  var newState = _objectSpread({}, reducedState); // only rehydrate if inboundState exists and is an object\n\n\n  if (inboundState && _typeof(inboundState) === 'object') {\n    Object.keys(inboundState).forEach(function (key) {\n      // ignore _persist data\n      if (key === '_persist') return; // if reducer modifies substate, skip auto rehydration\n\n      if (originalState[key] !== reducedState[key]) {\n        if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist/stateReconciler: sub state for key `%s` modified, skipping.', key);\n        return;\n      } // otherwise hard set the new value\n\n\n      newState[key] = inboundState[key];\n    });\n  }\n\n  if (process.env.NODE_ENV !== 'production' && debug && inboundState && _typeof(inboundState) === 'object') console.log(\"redux-persist/stateReconciler: rehydrated keys '\".concat(Object.keys(inboundState).join(', '), \"'\"));\n  return newState;\n}", "import { KEY_PREFIX, REHYDRATE } from './constants';\n// @TODO remove once flow < 0.63 support is no longer required.\nexport default function createPersistoid(config) {\n  // defaults\n  var blacklist = config.blacklist || null;\n  var whitelist = config.whitelist || null;\n  var transforms = config.transforms || [];\n  var throttle = config.throttle || 0;\n  var storageKey = \"\".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);\n  var storage = config.storage;\n  var serialize;\n\n  if (config.serialize === false) {\n    serialize = function serialize(x) {\n      return x;\n    };\n  } else if (typeof config.serialize === 'function') {\n    serialize = config.serialize;\n  } else {\n    serialize = defaultSerialize;\n  }\n\n  var writeFailHandler = config.writeFailHandler || null; // initialize stateful values\n\n  var lastState = {};\n  var stagedState = {};\n  var keysToProcess = [];\n  var timeIterator = null;\n  var writePromise = null;\n\n  var update = function update(state) {\n    // add any changed keys to the queue\n    Object.keys(state).forEach(function (key) {\n      if (!passWhitelistBlacklist(key)) return; // is keyspace ignored? noop\n\n      if (lastState[key] === state[key]) return; // value unchanged? noop\n\n      if (keysToProcess.indexOf(key) !== -1) return; // is key already queued? noop\n\n      keysToProcess.push(key); // add key to queue\n    }); //if any key is missing in the new state which was present in the lastState,\n    //add it for processing too\n\n    Object.keys(lastState).forEach(function (key) {\n      if (state[key] === undefined && passWhitelistBlacklist(key) && keysToProcess.indexOf(key) === -1 && lastState[key] !== undefined) {\n        keysToProcess.push(key);\n      }\n    }); // start the time iterator if not running (read: throttle)\n\n    if (timeIterator === null) {\n      timeIterator = setInterval(processNextKey, throttle);\n    }\n\n    lastState = state;\n  };\n\n  function processNextKey() {\n    if (keysToProcess.length === 0) {\n      if (timeIterator) clearInterval(timeIterator);\n      timeIterator = null;\n      return;\n    }\n\n    var key = keysToProcess.shift();\n    var endState = transforms.reduce(function (subState, transformer) {\n      return transformer.in(subState, key, lastState);\n    }, lastState[key]);\n\n    if (endState !== undefined) {\n      try {\n        stagedState[key] = serialize(endState);\n      } catch (err) {\n        console.error('redux-persist/createPersistoid: error serializing state', err);\n      }\n    } else {\n      //if the endState is undefined, no need to persist the existing serialized content\n      delete stagedState[key];\n    }\n\n    if (keysToProcess.length === 0) {\n      writeStagedState();\n    }\n  }\n\n  function writeStagedState() {\n    // cleanup any removed keys just before write.\n    Object.keys(stagedState).forEach(function (key) {\n      if (lastState[key] === undefined) {\n        delete stagedState[key];\n      }\n    });\n    writePromise = storage.setItem(storageKey, serialize(stagedState)).catch(onWriteFail);\n  }\n\n  function passWhitelistBlacklist(key) {\n    if (whitelist && whitelist.indexOf(key) === -1 && key !== '_persist') return false;\n    if (blacklist && blacklist.indexOf(key) !== -1) return false;\n    return true;\n  }\n\n  function onWriteFail(err) {\n    // @TODO add fail handlers (typically storage full)\n    if (writeFailHandler) writeFailHandler(err);\n\n    if (err && process.env.NODE_ENV !== 'production') {\n      console.error('Error storing data', err);\n    }\n  }\n\n  var flush = function flush() {\n    while (keysToProcess.length !== 0) {\n      processNextKey();\n    }\n\n    return writePromise || Promise.resolve();\n  }; // return `persistoid`\n\n\n  return {\n    update: update,\n    flush: flush\n  };\n} // @NOTE in the future this may be exposed via config\n\nfunction defaultSerialize(data) {\n  return JSON.stringify(data);\n}", "import { KEY_PREFIX } from './constants';\nexport default function getStoredState(config) {\n  var transforms = config.transforms || [];\n  var storageKey = \"\".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);\n  var storage = config.storage;\n  var debug = config.debug;\n  var deserialize;\n\n  if (config.deserialize === false) {\n    deserialize = function deserialize(x) {\n      return x;\n    };\n  } else if (typeof config.deserialize === 'function') {\n    deserialize = config.deserialize;\n  } else {\n    deserialize = defaultDeserialize;\n  }\n\n  return storage.getItem(storageKey).then(function (serialized) {\n    if (!serialized) return undefined;else {\n      try {\n        var state = {};\n        var rawState = deserialize(serialized);\n        Object.keys(rawState).forEach(function (key) {\n          state[key] = transforms.reduceRight(function (subState, transformer) {\n            return transformer.out(subState, key, rawState);\n          }, deserialize(rawState[key]));\n        });\n        return state;\n      } catch (err) {\n        if (process.env.NODE_ENV !== 'production' && debug) console.log(\"redux-persist/getStoredState: Error restoring data \".concat(serialized), err);\n        throw err;\n      }\n    }\n  });\n}\n\nfunction defaultDeserialize(serial) {\n  return JSON.parse(serial);\n}", "import { KEY_PREFIX } from './constants';\nexport default function purgeStoredState(config) {\n  var storage = config.storage;\n  var storageKey = \"\".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);\n  return storage.removeItem(storageKey, warnIfRemoveError);\n}\n\nfunction warnIfRemoveError(err) {\n  if (err && process.env.NODE_ENV !== 'production') {\n    console.error('redux-persist/purgeStoredState: Error purging data stored state', err);\n  }\n}", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport { FLUSH, PAUSE, PERSIST, PURGE, REHYDRATE, DEFAULT_VERSION } from './constants';\nimport autoMergeLevel1 from './stateReconciler/autoMergeLevel1';\nimport createPersistoid from './createPersistoid';\nimport defaultGetStoredState from './getStoredState';\nimport purgeStoredState from './purgeStoredState';\nvar DEFAULT_TIMEOUT = 5000;\n/*\n  @TODO add validation / handling for:\n  - persisting a reducer which has nested _persist\n  - handling actions that fire before reydrate is called\n*/\n\nexport default function persistReducer(config, baseReducer) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!config) throw new Error('config is required for persistReducer');\n    if (!config.key) throw new Error('key is required in persistor config');\n    if (!config.storage) throw new Error(\"redux-persist: config.storage is required. Try using one of the provided storage engines `import storage from 'redux-persist/lib/storage'`\");\n  }\n\n  var version = config.version !== undefined ? config.version : DEFAULT_VERSION;\n  var debug = config.debug || false;\n  var stateReconciler = config.stateReconciler === undefined ? autoMergeLevel1 : config.stateReconciler;\n  var getStoredState = config.getStoredState || defaultGetStoredState;\n  var timeout = config.timeout !== undefined ? config.timeout : DEFAULT_TIMEOUT;\n  var _persistoid = null;\n  var _purge = false;\n  var _paused = true;\n\n  var conditionalUpdate = function conditionalUpdate(state) {\n    // update the persistoid only if we are rehydrated and not paused\n    state._persist.rehydrated && _persistoid && !_paused && _persistoid.update(state);\n    return state;\n  };\n\n  return function (state, action) {\n    var _ref = state || {},\n        _persist = _ref._persist,\n        rest = _objectWithoutProperties(_ref, [\"_persist\"]); // $FlowIgnore need to update State type\n\n\n    var restState = rest;\n\n    if (action.type === PERSIST) {\n      var _sealed = false;\n\n      var _rehydrate = function _rehydrate(payload, err) {\n        // dev warning if we are already sealed\n        if (process.env.NODE_ENV !== 'production' && _sealed) console.error(\"redux-persist: rehydrate for \\\"\".concat(config.key, \"\\\" called after timeout.\"), payload, err); // only rehydrate if we are not already sealed\n\n        if (!_sealed) {\n          action.rehydrate(config.key, payload, err);\n          _sealed = true;\n        }\n      };\n\n      timeout && setTimeout(function () {\n        !_sealed && _rehydrate(undefined, new Error(\"redux-persist: persist timed out for persist key \\\"\".concat(config.key, \"\\\"\")));\n      }, timeout); // @NOTE PERSIST resumes if paused.\n\n      _paused = false; // @NOTE only ever create persistoid once, ensure we call it at least once, even if _persist has already been set\n\n      if (!_persistoid) _persistoid = createPersistoid(config); // @NOTE PERSIST can be called multiple times, noop after the first\n\n      if (_persist) {\n        // We still need to call the base reducer because there might be nested\n        // uses of persistReducer which need to be aware of the PERSIST action\n        return _objectSpread({}, baseReducer(restState, action), {\n          _persist: _persist\n        });\n      }\n\n      if (typeof action.rehydrate !== 'function' || typeof action.register !== 'function') throw new Error('redux-persist: either rehydrate or register is not a function on the PERSIST action. This can happen if the action is being replayed. This is an unexplored use case, please open an issue and we will figure out a resolution.');\n      action.register(config.key);\n      getStoredState(config).then(function (restoredState) {\n        var migrate = config.migrate || function (s, v) {\n          return Promise.resolve(s);\n        };\n\n        migrate(restoredState, version).then(function (migratedState) {\n          _rehydrate(migratedState);\n        }, function (migrateErr) {\n          if (process.env.NODE_ENV !== 'production' && migrateErr) console.error('redux-persist: migration error', migrateErr);\n\n          _rehydrate(undefined, migrateErr);\n        });\n      }, function (err) {\n        _rehydrate(undefined, err);\n      });\n      return _objectSpread({}, baseReducer(restState, action), {\n        _persist: {\n          version: version,\n          rehydrated: false\n        }\n      });\n    } else if (action.type === PURGE) {\n      _purge = true;\n      action.result(purgeStoredState(config));\n      return _objectSpread({}, baseReducer(restState, action), {\n        _persist: _persist\n      });\n    } else if (action.type === FLUSH) {\n      action.result(_persistoid && _persistoid.flush());\n      return _objectSpread({}, baseReducer(restState, action), {\n        _persist: _persist\n      });\n    } else if (action.type === PAUSE) {\n      _paused = true;\n    } else if (action.type === REHYDRATE) {\n      // noop on restState if purging\n      if (_purge) return _objectSpread({}, restState, {\n        _persist: _objectSpread({}, _persist, {\n          rehydrated: true\n        }) // @NOTE if key does not match, will continue to default else below\n\n      });\n\n      if (action.key === config.key) {\n        var reducedState = baseReducer(restState, action);\n        var inboundState = action.payload; // only reconcile state if stateReconciler and inboundState are both defined\n\n        var reconciledRest = stateReconciler !== false && inboundState !== undefined ? stateReconciler(inboundState, state, reducedState, config) : reducedState;\n\n        var _newState = _objectSpread({}, reconciledRest, {\n          _persist: _objectSpread({}, _persist, {\n            rehydrated: true\n          })\n        });\n\n        return conditionalUpdate(_newState);\n      }\n    } // if we have not already handled PERSIST, straight passthrough\n\n\n    if (!_persist) return baseReducer(state, action); // run base reducer:\n    // is state modified ? return original : return updated\n\n    var newState = baseReducer(restState, action);\n    if (newState === restState) return state;\n    return conditionalUpdate(_objectSpread({}, newState, {\n      _persist: _persist\n    }));\n  };\n}", "function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n/*\n  autoMergeLevel2: \n    - merges 2 level of substate\n    - skips substate if already modified\n    - this is essentially redux-perist v4 behavior\n*/\nexport default function autoMergeLevel2(inboundState, originalState, reducedState, _ref) {\n  var debug = _ref.debug;\n\n  var newState = _objectSpread({}, reducedState); // only rehydrate if inboundState exists and is an object\n\n\n  if (inboundState && _typeof(inboundState) === 'object') {\n    Object.keys(inboundState).forEach(function (key) {\n      // ignore _persist data\n      if (key === '_persist') return; // if reducer modifies substate, skip auto rehydration\n\n      if (originalState[key] !== reducedState[key]) {\n        if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist/stateReconciler: sub state for key `%s` modified, skipping.', key);\n        return;\n      }\n\n      if (isPlainEnoughObject(reducedState[key])) {\n        // if object is plain enough shallow merge the new values (hence \"Level2\")\n        newState[key] = _objectSpread({}, newState[key], {}, inboundState[key]);\n        return;\n      } // otherwise hard set\n\n\n      newState[key] = inboundState[key];\n    });\n  }\n\n  if (process.env.NODE_ENV !== 'production' && debug && inboundState && _typeof(inboundState) === 'object') console.log(\"redux-persist/stateReconciler: rehydrated keys '\".concat(Object.keys(inboundState).join(', '), \"'\"));\n  return newState;\n}\n\nfunction isPlainEnoughObject(o) {\n  return o !== null && !Array.isArray(o) && _typeof(o) === 'object';\n}", "import { combineReducers } from 'redux';\nimport persistReducer from './persistReducer';\nimport autoMergeLevel2 from './stateReconciler/autoMergeLevel2';\n// combineReducers + persistReducer with stateReconciler defaulted to autoMergeLevel2\nexport default function persistCombineReducers(config, reducers) {\n  config.stateReconciler = config.stateReconciler === undefined ? autoMergeLevel2 : config.stateReconciler;\n  return persistReducer(config, combineReducers(reducers));\n}", "function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { createStore } from 'redux';\nimport { FLUSH, PAUSE, PERSIST, PURGE, REGISTER, REHYDRATE } from './constants';\nvar initialState = {\n  registry: [],\n  bootstrapped: false\n};\n\nvar persistorReducer = function persistorReducer() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n\n  switch (action.type) {\n    case REGISTER:\n      return _objectSpread({}, state, {\n        registry: [].concat(_toConsumableArray(state.registry), [action.key])\n      });\n\n    case REHYDRATE:\n      var firstIndex = state.registry.indexOf(action.key);\n\n      var registry = _toConsumableArray(state.registry);\n\n      registry.splice(firstIndex, 1);\n      return _objectSpread({}, state, {\n        registry: registry,\n        bootstrapped: registry.length === 0\n      });\n\n    default:\n      return state;\n  }\n};\n\nexport default function persistStore(store, options, cb) {\n  // help catch incorrect usage of passing PersistConfig in as PersistorOptions\n  if (process.env.NODE_ENV !== 'production') {\n    var optionsToTest = options || {};\n    var bannedKeys = ['blacklist', 'whitelist', 'transforms', 'storage', 'keyPrefix', 'migrate'];\n    bannedKeys.forEach(function (k) {\n      if (!!optionsToTest[k]) console.error(\"redux-persist: invalid option passed to persistStore: \\\"\".concat(k, \"\\\". You may be incorrectly passing persistConfig into persistStore, whereas it should be passed into persistReducer.\"));\n    });\n  }\n\n  var boostrappedCb = cb || false;\n\n  var _pStore = createStore(persistorReducer, initialState, options && options.enhancer ? options.enhancer : undefined);\n\n  var register = function register(key) {\n    _pStore.dispatch({\n      type: REGISTER,\n      key: key\n    });\n  };\n\n  var rehydrate = function rehydrate(key, payload, err) {\n    var rehydrateAction = {\n      type: REHYDRATE,\n      payload: payload,\n      err: err,\n      key: key // dispatch to `store` to rehydrate and `persistor` to track result\n\n    };\n    store.dispatch(rehydrateAction);\n\n    _pStore.dispatch(rehydrateAction);\n\n    if (boostrappedCb && persistor.getState().bootstrapped) {\n      boostrappedCb();\n      boostrappedCb = false;\n    }\n  };\n\n  var persistor = _objectSpread({}, _pStore, {\n    purge: function purge() {\n      var results = [];\n      store.dispatch({\n        type: PURGE,\n        result: function result(purgeResult) {\n          results.push(purgeResult);\n        }\n      });\n      return Promise.all(results);\n    },\n    flush: function flush() {\n      var results = [];\n      store.dispatch({\n        type: FLUSH,\n        result: function result(flushResult) {\n          results.push(flushResult);\n        }\n      });\n      return Promise.all(results);\n    },\n    pause: function pause() {\n      store.dispatch({\n        type: PAUSE\n      });\n    },\n    persist: function persist() {\n      store.dispatch({\n        type: PERSIST,\n        register: register,\n        rehydrate: rehydrate\n      });\n    }\n  });\n\n  if (!(options && options.manualPersist)) {\n    persistor.persist();\n  }\n\n  return persistor;\n}", "import { DEFAULT_VERSION } from './constants';\nexport default function createMigrate(migrations, config) {\n  var _ref = config || {},\n      debug = _ref.debug;\n\n  return function (state, currentVersion) {\n    if (!state) {\n      if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: no inbound state, skipping migration');\n      return Promise.resolve(undefined);\n    }\n\n    var inboundVersion = state._persist && state._persist.version !== undefined ? state._persist.version : DEFAULT_VERSION;\n\n    if (inboundVersion === currentVersion) {\n      if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: versions match, noop migration');\n      return Promise.resolve(state);\n    }\n\n    if (inboundVersion > currentVersion) {\n      if (process.env.NODE_ENV !== 'production') console.error('redux-persist: downgrading version is not supported');\n      return Promise.resolve(state);\n    }\n\n    var migrationKeys = Object.keys(migrations).map(function (ver) {\n      return parseInt(ver);\n    }).filter(function (key) {\n      return currentVersion >= key && key > inboundVersion;\n    }).sort(function (a, b) {\n      return a - b;\n    });\n    if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: migrationKeys', migrationKeys);\n\n    try {\n      var migratedState = migrationKeys.reduce(function (state, versionKey) {\n        if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: running migration for versionKey', versionKey);\n        return migrations[versionKey](state);\n      }, state);\n      return Promise.resolve(migratedState);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  };\n}", "export default function createTransform( // @NOTE inbound: transform state coming from redux on its way to being serialized and stored\ninbound, // @NOTE outbound: transform state coming from storage, on its way to be rehydrated into redux\noutbound) {\n  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var whitelist = config.whitelist || null;\n  var blacklist = config.blacklist || null;\n\n  function whitelistBlacklistCheck(key) {\n    if (whitelist && whitelist.indexOf(key) === -1) return true;\n    if (blacklist && blacklist.indexOf(key) !== -1) return true;\n    return false;\n  }\n\n  return {\n    in: function _in(state, key, fullState) {\n      return !whitelistBlacklistCheck(key) && inbound ? inbound(state, key, fullState) : state;\n    },\n    out: function out(state, key, fullState) {\n      return !whitelistBlacklistCheck(key) && outbound ? outbound(state, key, fullState) : state;\n    }\n  };\n}", "export { default as persistReducer } from './persistReducer';\nexport { default as persistCombineReducers } from './persistCombineReducers';\nexport { default as persistStore } from './persistStore';\nexport { default as createMigrate } from './createMigrate';\nexport { default as createTransform } from './createTransform';\nexport { default as getStoredState } from './getStoredState';\nexport { default as createPersistoid } from './createPersistoid';\nexport { default as purgeStoredState } from './purgeStoredState';\nexport * from './constants';", "/**\r\n * Returns the value of object nested propery\r\n * without failing, even when the obeject is indefined\r\n * @export\r\n * @param {*} from Object to extract the property value from\r\n * @param {string | null} [path] a string path to the property (optional)\r\n * @param {*} [propDefault] a default value to return in case any of the path asertions fail (optional)\r\n * @returns {*} returns the value of the propety, or default value\r\n * @example\r\n * const obj = {\r\n *\tprop1: {\r\n *  \tprop2: \"test\",\r\n *    props3: null\r\n *  },\r\n *  props2: [1,2,3]\r\n * }\r\n *\r\n * console.log(\"should return 'test'\", ValueOf(obj, \"prop1.prop2\"));\r\n * console.log(\"should return [1,2,3]\", ValueOf(obj, \"props2\"));\r\n * console.log(\"should return 'default'\", ValueOf(obj, \"?prop1.prop4.nothing\", \"default\"));\r\n * console.log(\"should return 'null'\", ValueOf(obj, \"prop1.prop4.props3\", \"null\"));\r\n * console.log(\"should return undefined\", ValueOf(obj, \"prop3.prop2.prop1\"));\r\n */\r\nexport function ValueOf<T = any>(from: any, path?: string, propDefault?: any): T {\r\n  if (!Boolean(path)) return from || propDefault;\r\n  const passEmptyValues = /^\\?/.test(path || \"\");\r\n  const props = (path || \"\").replace(\"?\", \"\").split(\".\");\r\n  let result = from;\r\n  for (let i = 0; i < props.length; i++) {\r\n    if (result\r\n            && result[props[i]] !== undefined\r\n            && result[props[i]] !== null\r\n            && (passEmptyValues\r\n                || (\r\n                  result[props[i]] !== 0\r\n                    && result[props[i]] !== \"\"\r\n                )\r\n            )) {\r\n      result = result[props[i]];\r\n    } else {\r\n      result = propDefault;\r\n    }\r\n  }\r\n  return result;\r\n}\r\n", "export interface IAssertOptions {\r\n  error: string;\r\n  failEmptyString: Boolean;\r\n  failNullValue: Boolean;\r\n  failZeroValue: Boolean;\r\n  test: (target: any) => Boolean;\r\n}\r\n\r\nconst defaultOptions: IAssertOptions = {\r\n  error: \"ReferenceError: test is not defined\",\r\n  failEmptyString: true,\r\n  failNullValue: true,\r\n  failZeroValue: false,\r\n  test: () => false\r\n};\r\n\r\n/**\r\n * Insure the target has a value\r\n * or throw error if it does not\r\n * @export\r\n * @param {*} target value to be tested\r\n * @param {(IAssertOptions | string)} options test options, or error message string to be thrown\r\n * interface IAssertOptions {\r\n *    error: string; - error message string to be thrown\r\n *    failEmptyString: Boolean; - failt when value is an empty string (\"\")\r\n *    failNullValue: Boolean; - fail when value is null\r\n *    failZeroValue: Boolean; - fail when vaule is a zero (0)\r\n *    test: (target: any) => Boolean; - custom test to run (test must return true ONLY when value contains an error)\r\n * }\r\n * @returns {*} target\r\n * @example\r\n * const test = null;\r\n * Assert(test, \"test value can not be Null!\");\r\n * ... will throw console error:\r\n * test value can not be Null!\r\n */\r\nexport function Assert(target: any, options: IAssertOptions | string) {\r\n  const _opts: IAssertOptions = {} as IAssertOptions;\r\n  Object.assign(_opts, defaultOptions, typeof options === \"string\" ? {error: options} : options);\r\n  if (\r\n    target === undefined ||\r\n        _opts.failNullValue && target === null ||\r\n        _opts.failZeroValue && target === 0 ||\r\n        _opts.failEmptyString && target === \"\" ||\r\n        _opts.test(target)\r\n  ) throw _opts.error;\r\n  return target;\r\n}\r\n", "import { concat, ObservableInput, of } from \"rxjs\";\r\n\r\nimport { AjaxResponse, EWidgetStatus, Models, Volt } from \"../Models\";\r\nimport { Actions } from \"../Actions\";\r\nimport { Assert } from \"./Assert\";\r\nimport { ValueOf } from \"./ExtractProp\";\r\n\r\nlet _restrictions: Array<Volt.IRestriction> = [];\r\n\r\nfunction filterRestriction(restriction: Volt.IRestriction | Array<Volt.IRestriction>, isFinal = true): Array<any> {\r\n  const response: any[] = [];\r\n  if (<PERSON>olean(restriction)) {\r\n    _restrictions = Array.isArray(restriction)\r\n      ? restriction as Array<Volt.IRestriction>\r\n      : [restriction] as Array<Volt.IRestriction>;\r\n  }\r\n  if (Array.isArray(_restrictions) && _restrictions.length) {\r\n    response.push(\r\n      Actions.raiseRestriction(_restrictions.pop() as Volt.IRestriction)\r\n    );\r\n  }\r\n  if (isFinal) {\r\n    response.push(\r\n      Actions.setWidgetStatus(EWidgetStatus.RENDERED)\r\n    );\r\n  }\r\n  return response;\r\n}\r\n\r\n/**\r\n * Every PATCH call needs the same type of error\r\n * checking before it can proceed with the data\r\n * updates.\r\n * @export\r\n * @param {AjaxResponse<any>} response\r\n * @param {...(ObservableInput<T>)[]} observables\r\n * @returns {*}\r\n */\r\nexport function FilterRestrictionObservable<T>(response: AjaxResponse<Volt.IAPIResponse | Volt.IRestrictionAPIResponse>, ...observables: (ObservableInput<T>)[]): any {\r\n  Assert(response, \"Expected response object, but got undefined/null\");\r\n  Assert(observables, \"Expected Array<ObservableInput>, but got undefined/null\");\r\n  // Flow has been initilized\r\n  sessionStorage.setItem(\"omf:Initilized\", \"yes\");\r\n  // Check if there are any message from server\r\n  switch (true) {\r\n    // API has failed\r\n    case !(response.data && typeof response.data === \"object\"):\r\n      return [ // Not good V\r\n        Actions.errorOccured(new Models.ErrorHandler(response.statusText, { ...(typeof response.data === 'object' ? response.data : {}), url: response.url })),\r\n        Actions.setWidgetStatus(EWidgetStatus.RENDERED)\r\n      ];\r\n      // Final restriction handling:\r\n    case !!ValueOf(response, \"data.restriction\", false):\r\n      return filterRestriction(ValueOf(response, \"data.restriction\"));\r\n      // Proceed with normal flow\r\n    default:\r\n      const restrictionActions = filterRestriction(ValueOf(response, \"data.productOfferingDetail.restriction\"), false);\r\n      const genericOperations = [\r\n        Actions.broadcastUpdate(Actions.setProductConfigurationTotal(ValueOf(response, \"data.productOfferingDetail.productConfigurationTotal\")))\r\n      ];\r\n      \r\n      return concat(\r\n        // Non blocking restriction case\r\n        of(...restrictionActions),\r\n        // generic operations to run\r\n        of(...genericOperations),\r\n        // ...do everything else\r\n        ...observables);\r\n  }\r\n}\r\n", "import React from 'react';\r\nimport { useIntl } from 'react-intl';\r\n\r\nexport interface FormattedHTMLMessageProps {\r\n  id: string;\r\n  values?: Record<string, any>;\r\n  defaultMessage?: string;\r\n  children?: any;\r\n}\r\n\r\nexport const FormattedHTMLMessage: React.FC<FormattedHTMLMessageProps> = ({\r\n  id,\r\n  values = {},\r\n  defaultMessage\r\n}) => {\r\n  const intl = useIntl();\r\n\r\n  try {\r\n    let formatted = intl.formatMessage({ id, defaultMessage }, values);\r\n\r\n    // Replace any {key, number, CAD} manually with intl.formatNumber\r\n    const currencyRegex = /\\{(\\w+),\\s*number,\\s*CAD\\}/g;\r\n    formatted = formatted.replace(currencyRegex, (_, key) => {\r\n      const value = values[key];\r\n      if (value != null && !isNaN(value)) {\r\n        return intl.formatNumber(value, {\r\n          style: 'currency',\r\n          currency: 'CAD',\r\n        });\r\n      }\r\n      return '';\r\n    });\r\n\r\n    // Fallback manual replacement for simple {key}\r\n    if (formatted.includes('{') && formatted.includes('}') && values) {\r\n      Object.entries(values).forEach(([key, value]) => {\r\n        const placeholder = `{${key}}`;\r\n        formatted = formatted.replace(\r\n          new RegExp(placeholder.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'),\r\n          String(value)\r\n        );\r\n      });\r\n    }\r\n\r\n    return <span dangerouslySetInnerHTML={{ __html: formatted }} />;\r\n  } catch (e) {\r\n    console.warn(`Error formatting HTML message: ${id}`, e);\r\n    const fallbackText = intl.messages[id] || defaultMessage || id;\r\n    return <span dangerouslySetInnerHTML={{ __html: fallbackText }} />;\r\n  }\r\n};\r\n", "import { LocalizationState } from \"bwtk\";\r\nimport { persistReducer } from \"redux-persist\";\r\nimport hardSet from \"redux-persist/lib/stateReconciler/hardSet\";\r\nimport storage from \"redux-persist/lib/storage/session\";\r\nimport { EFlowType, EWidgetRoute } from \"../Models\";\r\nimport { ValueOf } from \"./ExtractProp\";\r\n\r\nexport namespace Utils {\r\n\r\n  declare const window: any;\r\n  declare const document: any;\r\n  /**\r\n   * Pick proper locale from the standard\r\n   * locale notation\r\n   * @export\r\n   * @param {LocalizationState} localization\r\n   * @returns {LocalizationState}\r\n   */\r\n  export function getCurrentLocale(localization: LocalizationState): LocalizationState {\r\n    const locale: string = localization.locale.substr(0, 2);\r\n    return { ...localization, formats: localization.formats[locale] as any, messages: localization.messages[locale] as any };\r\n  }\r\n\r\n  /**\r\n   * Open Lightbox and pass subscriberID\r\n   * @export\r\n   * @params {subscriberId}\r\n   */\r\n  declare function $(prop: string): any;\r\n  export function showLightbox(lightboxId: string) {\r\n    const modal = $(\"#\" + lightboxId);\r\n    modal.modal(\"show\");\r\n  }\r\n  export function isLightboxOpen(lightboxId: string) {\r\n    const modal = $(\"#\" + lightboxId);\r\n    return ValueOf(modal.data(\"bs.modal\"), \"_isShown\", false);\r\n  }\r\n  export function hideLightbox(lightboxId: string) {\r\n    if (isLightboxOpen(lightboxId)) {\r\n      const modal = $(\"#\" + lightboxId);\r\n      modal.modal(\"hide\");\r\n    }\r\n  }\r\n\r\n  export function getCookie(cname: string) {\r\n    const decodedCookie = decodeURIComponent(document.cookie);\r\n    const name = cname + \"=\";\r\n    const ca = decodedCookie.split(\";\");\r\n    for (let i = 0; i < ca.length; i++) {\r\n      let c = ca[i];\r\n      while (c.charAt(0) === \" \") {\r\n        c = c.substring(1);\r\n      }\r\n      if (c.indexOf(name) === 0) {\r\n        return c.substring(name.length, c.length);\r\n      }\r\n    }\r\n    return \"\";\r\n  }\r\n\r\n  export const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;\r\n  export const isIE11 = !!window.MSInputMethodContext && !!document.documentMode;\r\n\r\n  export const deviceType = () => {\r\n    const windowWidth = $(window).outerWidth();\r\n    const isMobile = windowWidth > 767 ? false : true;\r\n    const isTablet = windowWidth > 991 ? false : true;\r\n    return {\r\n      isMobile,\r\n      isTablet\r\n    };\r\n  };\r\n\r\n  export function debounce(func: Function, wait: number, immediate?: boolean) {\r\n    let timeout: any;\r\n    return function () {\r\n      const context = this, args = arguments;\r\n      const later = function () {\r\n        timeout = null;\r\n        if (!immediate) func.apply(context, args);\r\n      };\r\n      const callNow = immediate && !timeout;\r\n      clearTimeout(timeout);\r\n      timeout = setTimeout(later, wait);\r\n      if (callNow) func.apply(context, args);\r\n    };\r\n  }\r\n\r\n  /*\r\n   *  @description the caller is the reducer accessor method of the BaseStore class of individual consuming widget\r\n   *  @param {any} config persistConfig object, \"key\" property value is the \"name\" value of individual consuming widget from its package.json\r\n   *  @param {any} reducers combined reducers of individual consuming widget\r\n   *  @return {any} persisted reducers required by Redux-persist\r\n   *  usage example:\r\n   *\r\n   *    import { Utils } from \"omf-changepackage-components\";\r\n   *    export class Store extends BaseStore<IStoreState> {\r\n   *       constructor(store: BwtkStore, private epics: Epics, private localization: Localization) {\r\n   *         super(store);\r\n   *       }\r\n   *\r\n   *       get reducer() {\r\n   *\r\n   *         return Utils.reducer(\r\n   *           {\r\n   *              ...Utils.persistConfig,\r\n   *              key: require(\"../../package.json\").name,\r\n   *              blacklist: [\"uiValidationErrors\"]\r\n   *           },\r\n   *           combineReducers<IStoreState>({\r\n   *             ...\r\n   */\r\n  export const reducer = (config: any, reducers: any): any => persistReducer(config, reducers);\r\n\r\n  export const persistStateExists = (config: any): boolean => Boolean(localStorage.getItem(`omf:${config.key}`));\r\n\r\n  export const clearCachedState = (widgets: string[]): void => {\r\n    widgets.forEach((widget: string) => sessionStorage.removeItem(`omf:${widget}`));\r\n  };\r\n\r\n  /*\r\n   *  @description to be merged into the config object of individual widgets with their unique key value and black/whitelist if any\r\n   */\r\n  export const persistConfig: any = (name: string, blacklist: Array<string> = []) => ({\r\n    version: 1,\r\n    keyPrefix: \"omf:\",\r\n    storage,\r\n    stateReconciler: hardSet,\r\n    key: name,\r\n    blacklist: [\"localization\", \"widgetStatus\", \"error\", \"lightboxData\", \"restriction\", ...blacklist]\r\n  });\r\n\r\n  export function getFlowType(): EFlowType {\r\n    let flowType = sessionStorage.getItem(\"omf:Flowtype\") as EFlowType;\r\n    if (!flowType) {\r\n      const pathname = window.location.pathname;\r\n      switch (true) {\r\n        case pathname.indexOf(\"Changepackage/Internet\") > 0:\r\n          flowType = EFlowType.INTERNET;\r\n          break;\r\n        case pathname.indexOf(\"Changepackage/TV\") > 0:\r\n          flowType = EFlowType.TV;\r\n          break;\r\n        case pathname.indexOf(\"Add/TV\") > 0:\r\n          flowType = EFlowType.ADDTV;\r\n          break;\r\n        case pathname.indexOf(\"Bundle/\") > 0:\r\n          flowType = EFlowType.BUNDLE;\r\n          break;\r\n      }\r\n    }\r\n    return flowType;\r\n  }\r\n\r\n  export function constructPageRoute(route?: EWidgetRoute, flowType?: EFlowType): string {\r\n    flowType = flowType || getFlowType();\r\n    switch (flowType) {\r\n      case EFlowType.INTERNET:\r\n        switch (route) {\r\n          case EWidgetRoute.INTERNET: return \"/Changepackage/Internet\";\r\n          case EWidgetRoute.APPOINTMENT: return \"/Changepackage/Internet/Appointment\";\r\n          case EWidgetRoute.REVIEW: return \"/Changepackage/Internet/Review\";\r\n          case EWidgetRoute.CONFIRMATION: return \"/Changepackage/Internet/Confirmation\";\r\n          default: return \"/Changepackage\";\r\n        }\r\n      case EFlowType.TV:\r\n        switch (route) {\r\n          case EWidgetRoute.TV: return \"/Changepackage/TV\";\r\n          case EWidgetRoute.APPOINTMENT: return \"/Changepackage/TV/Appointment\";\r\n          case EWidgetRoute.REVIEW: return \"/Changepackage/TV/Review\";\r\n          case EWidgetRoute.CONFIRMATION: return \"/Changepackage/TV/Confirmation\";\r\n          default: return \"/Changepackage\";\r\n        }\r\n      case EFlowType.ADDTV:\r\n        switch (route) {\r\n          case EWidgetRoute.TV: return \"/Add/TV\";\r\n          case EWidgetRoute.REVIEW: return \"/Add/TV/Review\";\r\n          case EWidgetRoute.CONFIRMATION: return \"/Add/TV/Confirmation\";\r\n          default: return \"/Add\";\r\n        }\r\n      case EFlowType.BUNDLE:\r\n        switch (route) {\r\n          case EWidgetRoute.INTERNET: return \"/Bundle/Internet\";\r\n          case EWidgetRoute.TV: return \"/Bundle/TV\";\r\n          case EWidgetRoute.APPOINTMENT: return \"/Bundle/Internet/Appointment\";\r\n          case EWidgetRoute.REVIEW: return \"/Bundle/Review\";\r\n          case EWidgetRoute.CONFIRMATION: return \"/Bundle/Confirmation\";\r\n          default: return \"/Bundle\";\r\n        }\r\n    }\r\n  }\r\n\r\n  export function getPageRoute(): EWidgetRoute | undefined {\r\n    const pathname = window.location.pathname;\r\n    switch (true) {\r\n      case pathname.indexOf(\"Review\") > 0: return EWidgetRoute.REVIEW;\r\n      case pathname.indexOf(\"Confirm\") > 0: return EWidgetRoute.CONFIRMATION;\r\n      case pathname.indexOf(\"Appoint\") > 0: return EWidgetRoute.APPOINTMENT;\r\n      case pathname.indexOf(\"Internet\") > 0: return EWidgetRoute.INTERNET;\r\n      case pathname.indexOf(\"TV\") > 0: return EWidgetRoute.TV;\r\n    }\r\n    return undefined;\r\n  }\r\n\r\n  export function getURLByFlowType(scheme: { [key: string]: string }): string {\r\n    const flowType = sessionStorage.getItem(\"omf:Flowtype\") || \"Internet\";\r\n    return scheme[flowType];\r\n  }\r\n\r\n  export function appendRefreshOnce(path: string) {\r\n    const isFistTime = !sessionStorage.getItem(\"omf:Initilized\");\r\n    return (path || \"\") + (isFistTime ? \"?refreshCache=true\" : \"\");\r\n  }\r\n}\r\nexport * from \"./Assert\";\r\nexport * from \"./ExtractProp\";\r\nexport * from \"./FilterRestrictionObservable\";\r\n\r\nexport * from \"./FormattedHTMLMessage\";\r\n", "\r\nexport namespace Volt {\r\n\r\n  export interface IMessageButton {\r\n    buttonName: string;\r\n    href: string;\r\n    offeringAction: string;\r\n    orderItemType: string;\r\n  }\r\n\r\n  export interface IMessage {\r\n    messageCode: string;\r\n    messageTitle: string | null;\r\n    messageBody: string;\r\n    messageText: string;\r\n    type: string;\r\n    // messageLanguage: string | null;\r\n    messageType: \"error\" | \"Information\" | null;\r\n    buttons: Array<IMessageButton> | null;\r\n  }\r\n\r\n  export interface IRestriction {\r\n    type: string;\r\n    title: string;\r\n    description: string;\r\n    actionLinks: Array<IHypermediaAction>;\r\n    dynamicData: {\r\n      products: Array<string>;\r\n      promotions: Array<{\r\n        name: string,\r\n        price: string,\r\n        expiry: string\r\n      }>;\r\n    };\r\n    footerDescription: string;\r\n    onComplete?: (intent: string) => void;\r\n  }\r\n\r\n  export interface IHypermediaActionMessageOption {\r\n    id: string;\r\n    patch: string;\r\n    description: string;\r\n  }\r\n\r\n  export interface IHypermediaActionMessageBody {\r\n    op: string;\r\n    value: Array<IHypermediaActionMessageOption>;\r\n  }\r\n\r\n  export interface IHypermediaAction {\r\n    rel: string;\r\n    href: string;\r\n    method: \"GET\" | \"PUT\" | \"POST\" | \"PATCH\" | \"DELETE\";\r\n    messageBody?: IHypermediaActionMessageBody;\r\n    name?: any;\r\n    redirectURLKey?: any;\r\n  }\r\n\r\n  export enum EOfferingState {\r\n    // TV\r\n    Add = \"add\",\r\n    Delete = \"delete\",\r\n    Modify = \"modify\",\r\n    NoCharge = \"noChange\",\r\n    // Internet\r\n    Remove = \"Remove\",\r\n    Change = \"Change\",\r\n    InitiallySelected = \"InitiallySelected\",\r\n    NewlySelected = \"NewlySelected\",\r\n    Removed = \"removed\",\r\n    NotSelected = \"NotSelected\",\r\n    Added = \"Added\",\r\n    UnSelected = \"UnSelected\",\r\n    Create = \"Create\",\r\n    NoChange = \"NoChange\"\r\n  }\r\n\r\n  export enum EOfferingType {\r\n    BaseOffering = \"BaseOffering\",\r\n    GroupOffering = \"GroupOffering\",\r\n    SingleOffering = \"SingleOffering\"\r\n  }\r\n\r\n  export interface ICharacteristic {\r\n    name: string;\r\n    value: string;\r\n  }\r\n\r\n  export interface IPriceDetail {\r\n    price: number;\r\n    priceType: \"Recurring\" | \"OneTime\";\r\n  }\r\n\r\n  export interface IPromotionDetail {\r\n    description: string;\r\n    promotionalPrice: IPriceDetail;\r\n    discountPrice: IPriceDetail;\r\n    discountStartMonth: string;\r\n    discountDuration: number;\r\n    expiryDate: string;\r\n    legalMessage: string;\r\n    state: EOfferingState;\r\n  }\r\n\r\n  export enum EDIsplayGroupKey {\r\n    TV_BASE_PRODUCT = \"TV_BASE_PRODUCT\",\r\n    ALACARTE = \"ALACARTE\",\r\n    MOVIE = \"MOVIE\",\r\n    TV = \"TV\",\r\n    SPECIALITY_SPORTS = \"SPECIALITY_SPORTS\",\r\n    ADD_ON = \"ADD_ON\",\r\n    INTERNATIONAL = \"INTERNATIONAL\",\r\n    INTERNATIONAL_COMBOS = \"INTERNATIONAL_COMBOS\",\r\n    INTERNATIONAL_ALACARTE = \"INTERNATIONAL_ALACARTE\",\r\n    BASE_PROGRAMMING = \"BASE_PROGRAMMING\",\r\n    SPECIALITY_CHANNELS = \"SPECIALITY_CHANNELS\",\r\n    OFFERS = \"OFFERS\",\r\n    TV_BROWSE_ALL = \"TV_BROWSE_ALL\",\r\n    PROMOTION = \"PROMOTION\",\r\n    NONE = \"NONE\"\r\n  }\r\n\r\n  export enum EProductOfferingType {\r\n    PACKAGE = \"BasePackage\",\r\n    COMBO = \"Combo\",\r\n    CHANNEL = \"Channel\",\r\n    NONE = \"None\"\r\n  }\r\n\r\n  export interface IProductOffering {\r\n    id: string;\r\n    name: string;\r\n    imagePath?: string;\r\n    longDescription: string;\r\n    shortDescription: string;\r\n    usagePlan: string;\r\n    productPath: string;\r\n    channelNumber: string;\r\n    state: EOfferingState;\r\n    type: EOfferingType;\r\n    language: string;\r\n    isSelectable: boolean;\r\n    isCurrent: boolean;\r\n    isSelected: boolean;\r\n    isDisabled: boolean;\r\n    characteristics: Array<ICharacteristic>;\r\n    regularPrice: IPriceDetail;\r\n    promotionDetails: IPromotionDetail;\r\n    // productOfferingAction: Volt.IHypermediaAction;\r\n    productOfferingType: EProductOfferingType;\r\n    offeringAction: Volt.IHypermediaAction;\r\n    actionLink: Volt.IHypermediaAction;\r\n    countOfOffering?: number;\r\n    channels: string[];\r\n    childOfferings: IProductOffering[];\r\n    displayGroupKey: EDIsplayGroupKey;\r\n    multipleWaysToAdd: Array<string>;\r\n    sortPriority: number;\r\n    isAlreadyIncludedIn: string;\r\n  }\r\n\r\n  export interface IProductCatalog {\r\n    productOfferings: Array<IProductOffering>;\r\n    productConfigurationTotal: IProductConfigurationTotal;\r\n  }\r\n\r\n  export interface IProductConfigurationTotal {\r\n    priceOvertime: Array<ILineOfBusiness>;\r\n    summaryAction: IHypermediaAction;\r\n    resetAction: IHypermediaAction;\r\n    nextAction: IHypermediaAction;\r\n    productOfferingCount: number;\r\n  }\r\n\r\n  export interface IChargeDetail {\r\n    name: string;\r\n    priceDetail: IPriceDetail;\r\n  }\r\n\r\n  export interface ILineOfBusiness {\r\n    currentPrice: IPriceDetail;\r\n    newPrice: IPriceDetail;\r\n    regularCurrentPrice?: IPriceDetail;\r\n    regularNewPrice?: IPriceDetail;\r\n    flowType: \"TV\" | \"Internet\" | \"AllLOBs\";\r\n  }\r\n\r\n  export enum EProductOfferingGroupType {\r\n    Delta = \"Delta\",\r\n    New = \"New\",\r\n    Current = \"Current\",\r\n    Default = \"Default\"\r\n  }\r\n\r\n  export enum ELineOfBusiness {\r\n    TV = \"TV\",\r\n    Internet = \"Internet\"\r\n  }\r\n\r\n  export interface IProductOfferingGroup {\r\n    productOfferingGroupType: EProductOfferingGroupType;\r\n    productOfferingGroupTotal: IPriceDetail | null;\r\n    additionalCharges: Array<IChargeDetail> | null;\r\n    productOfferings: Array<IProductOffering>;\r\n    lineOfBusiness: ELineOfBusiness | null;\r\n  }\r\n\r\n  export interface IPhoneDetails {\r\n    phoneNumber: string;\r\n    extension: string;\r\n  }\r\n\r\n  export enum EAppointmentDuration {\r\n    AM = \"AM\",\r\n    PM = \"PM\",\r\n    Evening = \"Evening\",\r\n    AllDay = \"AllDay\",\r\n    Item0810 = \"Item0810\",\r\n    Item1012 = \"Item1012\",\r\n    Item1315 = \"Item1315\",\r\n    Item1517 = \"Item1517\",\r\n    Item1719 = \"Item1719\",\r\n    Item1921 = \"Item1921\"\r\n  }\r\n\r\n  export enum EPreferredContactMethod {\r\n    EMAIL = \"Email\",\r\n    TEXT_MESSAGE = \"TextMessage\",\r\n    PHONE = \"Phone\"\r\n  }\r\n\r\n  export interface IAppointmentDetail {\r\n    availableDates?: Array<IAvailableDates>;\r\n    preferredDate?: any;\r\n    duration?: EAppointmentDuration;\r\n    installationAddress?: IInstallationAddress;\r\n    contactInformation?: IContactInformation;\r\n    additionalDetails?: IAdditionalDetails;\r\n    isInstallationRequired?: boolean;\r\n  }\r\n\r\n\r\n  export interface IAvailableDates {\r\n    date: string;\r\n    timeSlots: Array<ITimeSlots>;\r\n    isPreferredDate?: boolean;\r\n  }\r\n\r\n  export interface IInstallationAddress {\r\n    address1?: string;\r\n    address2?: string;\r\n    city: string;\r\n    province: string;\r\n    postalCode: string;\r\n  }\r\n\r\n  export interface IContactInformation {\r\n    preferredContactMethod?: EPreferredContactMethod;\r\n    primaryPhone?: IPrimaryPhone;\r\n    additionalPhone?: string;\r\n    textMessage?: string;\r\n    email?: string;\r\n  }\r\n\r\n  export interface ITimeSlots {\r\n    intervalType: EAppointmentDuration;\r\n    timeInterval: ITimeInterval;\r\n    isAvailable: boolean;\r\n    isSelected: boolean;\r\n  }\r\n\r\n  export interface IPrimaryPhone {\r\n    phoneNumber: string;\r\n    phoneExtension?: string;\r\n  }\r\n\r\n  export interface IAdditionalDetails {\r\n\r\n  }\r\n\r\n  export interface ITimeInterval {\r\n    startTime: number;\r\n    endTime: number;\r\n  }\r\n\r\n\r\n  export interface IDisplayGroupOffering {\r\n    // New\r\n    key: EDIsplayGroupKey;\r\n    offeringId: string;\r\n    offeringName: string;\r\n    parentId: string | null;\r\n    parentKey: string | null;\r\n    sortPriority: number;\r\n    isRoot: boolean;\r\n\r\n    // Inherited\r\n    count: number;\r\n    subTotalPrice: IPriceDetail;\r\n\r\n    // Old\r\n    name: string;\r\n    offeringKey: EDIsplayGroupKey;\r\n    parentDisplayGroup?: EDIsplayGroupKey;\r\n  }\r\n\r\n  export interface IDisplayGroup {\r\n    baseOffering: IDisplayGroupOffering;\r\n    additionalOfferings: Array<IDisplayGroupOffering>;\r\n  }\r\n\r\n  export interface IProductOfferingDetail {\r\n    messages: Array<IMessage> | null;\r\n    productOfferingGroups: Array<IProductOfferingGroup>;\r\n    productConfigurationTotal: IProductConfigurationTotal;\r\n    restriction: IRestriction;\r\n    // nothing is final, let's use any for everything for now\r\n    appointment?: IAppointmentDetail;\r\n    customerInformation?: any;\r\n    displayGroup?: IDisplayGroup;\r\n  }\r\n\r\n  export interface IAPIResponse {\r\n    confirmationNumber: string | null;\r\n    orderDate: string | null;\r\n    productOfferingDetail: IProductOfferingDetail;\r\n  }\r\n\r\n  export interface IRestrictionAPIResponse {\r\n    restriction: IRestriction;\r\n    redirectURLKey: string;\r\n  }\r\n}\r\n", "import { LocalizationState } from \"bwtk\";\r\nimport { merge, of , ObservableInput } from \"rxjs\";\r\nimport { Utils } from \"../Utils\";\r\n\r\nimport { Actions } from \"../Actions\";\r\nimport { Volt } from \"./VOLT\";\r\n\r\nexport * from \"./VOLT\";\r\n\r\nexport enum EModals {\r\n  PREVIEWMODAL = \"PREVIEW_MODAL\"\r\n}\r\n\r\nexport enum EWidgetStatus {\r\n  INIT,\r\n  RENDERED,\r\n  UPDATING,\r\n  ERROR,\r\n  OUTAGERROR\r\n}\r\n\r\nexport enum EWidgetRoute {\r\n  INTERNET = \"/Internet\",\r\n  TV = \"/TV\",\r\n  TV_Packages = \"/Packages\",\r\n  TV_MoviesSeries = \"/Movies\",\r\n  TV_Addons = \"/Addons\",\r\n  TV_Alacarte = \"/Alacarte\",\r\n  TV_International = \"/International\",\r\n  TV_InternationalCombos = \"/International/Combos\",\r\n  TV_InternationalAlacarte = \"/International/Alacarte\",\r\n  TV_Browse = \"/Browse\",\r\n  TV_Search = \"/Search\",\r\n  APPOINTMENT = \"/Appointment\",\r\n  REVIEW = \"/Review\",\r\n  CONFIRMATION = \"/Confirmation\"\r\n}\r\n\r\nexport enum EReviewMode {\r\n  Summary = \"summary\",\r\n  Review = \"review\",\r\n  Confirmation = \"confirmation\"\r\n}\r\n\r\nexport enum EWidgetName {\r\n  NAVIGATION = \"omf-changepackage-navigation\",\r\n  INTERNET = \"omf-changepackage-internet\",\r\n  TV = \"omf-changepackage-tv\",\r\n  APPOINTMENT = \"omf-changepackage-appointment\",\r\n  // Preview, Review and Confirmation are done using the same widget!\r\n  PREVIEW = \"omf-changepackage-review\",\r\n  REVIEW = \"omf-changepackage-review\",\r\n  CONFIRMATION = \"omf-changepackage-review\"\r\n}\r\n\r\nexport enum EFlowType {\r\n  INTERNET = \"Internet\",\r\n  TV = \"TV\",\r\n  ADDTV = \"AddTV\",\r\n  BUNDLE = \"Bundle\"\r\n}\r\n\r\nexport interface AjaxResponse<T> {\r\n  headers: {\r\n    [key: string]: any;\r\n  };\r\n  redirected: boolean;\r\n  status: number;\r\n  statusText: string;\r\n  type: string;\r\n  url: string;\r\n  dataType: string;\r\n  data: T;\r\n}\r\n\r\n\r\nexport interface ILightboxPayload {\r\n  lightboxId: string;\r\n  data?: any;\r\n}\r\n\r\nexport namespace Models {\r\n\r\n  /**\r\n   * Bell brands enumerator\r\n   * @export\r\n   * @interface IBaseWidgetAPI\r\n   */\r\n  export enum EBrand {\r\n    BELL = \"B\",\r\n    VIRGIN = \"V\",\r\n    LUCKY = \"L\"\r\n  }\r\n\r\n  /**\r\n   * Base set of params passed into\r\n   * the widget via config system\r\n   * @export\r\n   * @interface IBaseEnvironmentVariables\r\n   */\r\n  export interface IBaseEnvironmentVariables {\r\n    province: string;\r\n    brand: EBrand;\r\n    language: \"en\" | \"en-ca\" | \"fr\" | \"fr-ca\";\r\n    transactionIdentifier: string;\r\n    useMockData: boolean;\r\n  }\r\n\r\n  /**\r\n   * Basic widget config property set\r\n   * @export\r\n   * @interface IBaseConfig\r\n   */\r\n  export interface IBaseConfig {\r\n    api: IBaseWidgetAPI;\r\n    headers: { [key: string]: string };\r\n    mockdata?: { [key: string]: { [key: string]: any } };\r\n    environmentVariables: IBaseEnvironmentVariables;\r\n  }\r\n\r\n  /**\r\n   * props used in every common\r\n   * component implementstion\r\n   * @export\r\n   * @interface IBaseComponentProps\r\n   */\r\n  export interface IBaseComponentProps {\r\n    id?: string;\r\n    className?: string;\r\n    style?: { [key: string]: string };\r\n  }\r\n\r\n  /**\r\n  * props passed to hte widget from it's parent\r\n  * or on DomRender\r\n  * @export\r\n  * @interface IBaseWidgetProps\r\n  */\r\n  export interface IBaseWidgetProps {\r\n  }\r\n\r\n  export interface IBaseWidgetAPI {\r\n    base: string;\r\n  }\r\n\r\n  /**\r\n   *\r\n   *\r\n   * @export\r\n   * @interface IBaseStoreState\r\n   */\r\n  export interface IBaseStoreState {\r\n    localization: LocalizationState;\r\n    widgetStatus: EWidgetStatus;\r\n    lightboxData?: any;\r\n    restriction?: Volt.IMessage;\r\n    error: IErrorHandlerProps;\r\n  }\r\n\r\n  /**\r\n   *\r\n   *\r\n   * @export\r\n   * @interface IBaseAppProps\r\n   */\r\n  export interface IBaseAppProps {\r\n    localization: LocalizationState;\r\n    widgetStatus: EWidgetStatus;\r\n    errorHandlerProps: IErrorHandlerProps;\r\n  }\r\n\r\n  /**\r\n   * immutable props set onto widget\r\n   * context and accessible from\r\n   * any component\r\n   * @export\r\n   * @interface IWidgetContext\r\n   */\r\n  export interface IWidgetContext<T> {\r\n    config: T;\r\n  }\r\n\r\n  /**\r\n   * immutable props set onto widget\r\n   * context and accessible from\r\n   * any component\r\n   * @export\r\n   * @interface IWidgetContext\r\n   */\r\n  export interface IWidgetContext<T> {\r\n    config: T;\r\n  }\r\n\r\n  /**\r\n   * report execuion errors\r\n   * @export\r\n   * @interface IErrorHandlerProps\r\n   */\r\n  export interface IErrorHandlerProps extends Error {\r\n    type: \"API\" | \"widget\" | \"logic\";\r\n    response?: Response;\r\n    componentStack?: any;\r\n    debug: boolean;\r\n  }\r\n  export class ErrorHandler extends Error implements IErrorHandlerProps {\r\n    type: \"API\" | \"widget\" | \"logic\";\r\n    response?: Response;\r\n    componentStack?: any;\r\n    debug: boolean = false;\r\n    constructor(message: string | Error, details?: any, debug: boolean = false) {\r\n      super(typeof message === \"object\" ? message.message : message);\r\n      this.debug = debug || Boolean(Utils.getCookie(\"debugwidget\"));\r\n      if (typeof message === \"object\") {\r\n        this.stack = (message as Error).stack;\r\n      }\r\n      if (typeof details === \"object\") {\r\n        switch (true) {\r\n          case Boolean(details[\"url\"]):\r\n            this.type = \"API\";\r\n            this.response = details;\r\n            break;\r\n          case Boolean(details[\"componentStack\"]):\r\n            this.type = \"widget\";\r\n            this.componentStack = details.componentStack;\r\n            break;\r\n        }\r\n      } else {\r\n        this.type = \"logic\";\r\n      }\r\n    }\r\n  }\r\n\r\n  export function ErrorHandlerObservable(action: any) {\r\n    return function (response: { error: Error }, source: ObservableInput<any>) {\r\n      const err: Error = response.error || response;\r\n      return merge(\r\n        of(Actions.errorOccured(new ErrorHandler(action.toString(), err))),\r\n        source\r\n      );\r\n    };\r\n  }\r\n\r\n  /* Regular Expressions */\r\n  export const noSpecialCharRegex = RegExp(\r\n    /^[a-zA-Z0-9]+$/i\r\n  );\r\n  export const emailRegex = RegExp(\r\n    /^[a-z0-9`!#\\$%&\\*\\+\\/=\\?\\^\\'\\-_]+((\\.)+[a-z0-9`!#\\$%&\\*\\+\\/=\\?\\^\\'\\-_]+)*@([a-z0-9]+([\\-][a-z0-9])*)+([\\.]([a-z0-9]+([\\-][a-z0-9])*)+)+$/i\r\n  );\r\n  export const phoneRegex = RegExp(\r\n    /^[0-9]\\d{2}-\\d{3}-\\d{4}$/i\r\n  );\r\n  export const hashCommaRegex = RegExp(\r\n    /[\\,\\#]/i\r\n  );\r\n  export const onlyNumbersRegex = RegExp(\r\n    /^[0-9]+$/i\r\n  );\r\n}\r\n", "import { Injectable, AjaxServices, CommonFeatures, AjaxOptions } from \"bwtk\";\r\n\r\nimport { Observable, Observer } from \"rxjs\";\r\nimport { Volt, Models } from \"../Models\";\r\nimport { Utils, ValueOf } from \"../Utils\";\r\n\r\nconst _isDebug = Boolean(Utils.getCookie(\"debugwidget\"));\r\n\r\nfunction createObservableRequest(method: () => Promise<any>) {\r\n  return new Observable((observer: Observer<any>) => {\r\n    method()\r\n      .then(response => {\r\n        observer.next(response);\r\n      })\r\n      .catch(error => {\r\n        observer.error(error);\r\n      })\r\n      .then(() => {\r\n        observer.complete();\r\n      });\r\n  });\r\n}\r\n\r\nfunction mockResponder(data: any): Observable<any> {\r\n  return createObservableRequest(() => new Promise<any>((resolve) => {\r\n    setTimeout(() => resolve({ data }), 350);\r\n  }));\r\n}\r\n\r\nfunction cleanPath(path: string): string {\r\n  return (path || \"\").split(\"/\").pop() as string;\r\n}\r\n\r\n/**\r\n * Base client implementation\r\n* Ajax client wrapper inplementing Graph specific calls\r\n* @export\r\n* @abstract\r\n* @class Client\r\n* @extends {CommonFeatures.BaseClient}\r\n*/\r\n@Injectable\r\nexport abstract class BaseClient extends CommonFeatures.BaseClient {\r\n  /**\r\n     * Creates an instance of Client.\r\n     * @param {AjaxServices} ajax\r\n     * @param {Models.IBaseConfig} config\r\n     * @memberof Client\r\n     */\r\n  constructor(ajax: AjaxServices, private config: Models.IBaseConfig) {\r\n    super(ajax);\r\n  }\r\n  private get useMockData(): boolean {\r\n    return (_isDebug && this.config.mockdata !== undefined) ||\r\n            (this.config.mockdata !== undefined && this.config.environmentVariables.useMockData);\r\n  }\r\n\r\n  private get mockdata(): { [key: string]: { [key: string]: any } } {\r\n    return this.config.mockdata || {};\r\n  }\r\n\r\n  // @ts-ignore - RxJS version conflict between main project and bwtk\r\n  public get<T>(path: string, _data?: any, _options?: AjaxOptions | undefined): Observable<T> {\r\n    const mock = ValueOf(this.mockdata, cleanPath(path) + \".GET\", false);\r\n    return this.useMockData &&\r\n            mock ?\r\n      mockResponder(mock) as any\r\n      : super.get.apply(this, arguments) as any;\r\n  }\r\n\r\n  // @ts-ignore - RxJS version conflict between main project and bwtk\r\n  public put<T>(path: string, _data: any, _options?: AjaxOptions | undefined): Observable<T> {\r\n    const mock = ValueOf(this.mockdata, cleanPath(path) + \".PUT\", false);\r\n    return this.useMockData &&\r\n            mock ?\r\n      mockResponder(mock) as any\r\n      : super.put.apply(this, arguments) as any;\r\n  }\r\n\r\n  // @ts-ignore - RxJS version conflict between main project and bwtk\r\n  public post<T>(path: string, _data: any, _options?: AjaxOptions | undefined): Observable<T> {\r\n    const mock = ValueOf(this.mockdata, cleanPath(path) + \".POST\", false);\r\n    return this.useMockData &&\r\n            mock ?\r\n      mockResponder(mock) as any\r\n      : super.post.apply(this, arguments) as any;\r\n  }\r\n\r\n  // @ts-ignore - RxJS version conflict between main project and bwtk\r\n  public del(path: string, _options?: AjaxOptions | undefined): any {\r\n    const mock = ValueOf(this.mockdata, cleanPath(path) + \".DELETE\", false);\r\n    return this.useMockData &&\r\n            mock ?\r\n      mockResponder(mock) as any\r\n      : super.del.apply(this, arguments) as any;\r\n  }\r\n\r\n  public action<T>(action: Volt.IHypermediaAction): Observable<T> {\r\n    switch (action.method) {\r\n      case \"PUT\": return this.put(action.href, action.messageBody);\r\n      case \"POST\": return this.post(action.href, action.messageBody);\r\n      case \"DELETE\": return this.del(action.href) as any;\r\n      case \"GET\":\r\n      default:\r\n        return this.get(action.href, action.messageBody);\r\n    }\r\n  }\r\n\r\n  get options() {\r\n    return {\r\n      url: this.config.api.base,\r\n      cache: false,\r\n      credentials: \"include\" as RequestCredentials,\r\n      headers: this.config.headers\r\n    };\r\n  }\r\n}\r\n", "import * as React from \"react\";\r\nimport { Models } from \"../Models\";\r\n\r\n/**\r\n * React Widget context provider\r\n * allows to propagate immutable values\r\n * through the widget componets\r\n * @export\r\n * @const WidgetContext\r\n * @extends {React.Context}\r\n */\r\nexport const WidgetContext = React.createContext<Models.IWidgetContext<Models.IBaseConfig | any>>({} as Models.IWidgetContext<Models.IBaseConfig>);\r\n\r\nexport const ContextProvider: React.ProviderExoticComponent<React.ProviderProps<Models.IWidgetContext<Models.IBaseConfig | any>>> = WidgetContext.Provider;\r\nexport const Context: React.ExoticComponent<React.ConsumerProps<Models.IWidgetContext<Models.IBaseConfig | any>>> = WidgetContext.Consumer;\r\nexport function withContext<T>(\r\n  Component: any\r\n): React.FC<T> {\r\n  return function (props: T) {\r\n    return (<Context>\r\n      {(context: Models.IWidgetContext<any>) => <Component {...props} {...context} />}\r\n    </Context>);\r\n  };\r\n}\r\n", "import { Epic, combineEpics, ofType } from \"redux-observable\";\r\nimport { mergeMap, tap } from \"rxjs\";\r\nimport { Actions } from \"../Actions\";\r\nimport { Models, EWidgetStatus } from \"../Models\";\r\nimport { Utils } from \"../Utils\";\r\n\r\nconst {\r\n  setWidgetStatus,\r\n  showHideLoader,\r\n  errorOccured,\r\n  clearCachedState\r\n} = Actions;\r\n\r\n/**\r\n * Basic widget lifecycle events\r\n * * Status update\r\n * * Error handeling\r\n * @export\r\n * @class LifecycleEpics\r\n */\r\nexport class LifecycleEpics {\r\n  combineEpics(): any {\r\n    return combineEpics(\r\n      this.onWidgetStatusEpic,\r\n      this.onErrorOccuredEpic,\r\n      this.clearCachedStateEpic\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Epic activated on widget state changes\r\n   *\r\n   * @readonly\r\n   * @private\r\n   * @type {GeneralEpic}\r\n   * @memberof Epics\r\n   */\r\n  private get onWidgetStatusEpic(): GeneralEpic {\r\n    return (action$) =>\r\n      action$.pipe(\r\n        ofType(setWidgetStatus.toString()),\r\n        mergeMap((action: ReduxActions.Action<EWidgetStatus>) => {\r\n          switch (action.payload) {\r\n            case EWidgetStatus.INIT: return [showHideLoader(true)];\r\n            case EWidgetStatus.UPDATING: return [showHideLoader(true)];\r\n            case EWidgetStatus.RENDERED: return [showHideLoader(false)];\r\n            case EWidgetStatus.ERROR: return [showHideLoader(false)];\r\n            default: return [];\r\n          }\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Epic activated in case of widget falure\r\n   *\r\n   * @readonly\r\n   * @private\r\n   * @type {ErrorOccuredEpic}\r\n   * @memberof Epics\r\n   */\r\n  private get onErrorOccuredEpic(): ErrorOccuredEpic {\r\n    return (action$) =>\r\n      action$.pipe(\r\n        ofType(errorOccured.toString()),\r\n        mergeMap(({ payload }: ReduxActions.Action<Models.IErrorHandlerProps>) => [\r\n          setWidgetStatus(EWidgetStatus.ERROR)\r\n        ])\r\n      );\r\n  }\r\n\r\n  private get clearCachedStateEpic(): GeneralEpic {\r\n    return (action$) =>\r\n      action$.pipe(\r\n        ofType(clearCachedState.toString()),\r\n        tap(({ payload }: ReduxActions.Action<string[]>) => {\r\n          Utils.clearCachedState(payload);\r\n        }),\r\n        // Epics must return an action, even after side effects\r\n        mergeMap(() => [])\r\n      );\r\n  }\r\n\r\n}\r\n\r\ntype GeneralEpic = Epic<ReduxActions.Action<any>, any>;\r\ntype ErrorOccuredEpic = Epic<ReduxActions.Action<any>, any>;\r\n", "import { Epic, combineEpics, ofType } from \"redux-observable\";\r\nimport { Action } from \"redux-actions\";\r\nimport { Actions } from \"../Actions\";\r\nimport { Utils, ValueOf, FilterRestrictionObservable } from \"../Utils\";\r\nimport { Volt } from \"../Models/VOLT\";\r\nimport { Models, AjaxResponse, EWidgetStatus } from \"../Models\";\r\nimport { BaseClient } from \"../Client\";\r\nimport { catchError, concat, EMPTY, filter, mergeMap, of } from \"rxjs\";\r\n\r\nconst {\r\n  raiseRestriction,\r\n  acceptRestriction,\r\n  declineRestriction,\r\n  finalizeRestriction,\r\n  broadcastUpdate,\r\n  setWidgetStatus,\r\n  historyGo\r\n} = Actions;\r\n\r\n/**\r\n * Sample general epic illustrating\r\n * API communication from outside\r\n * of widget cycle\r\n * @export\r\n * @class SampleEpics\r\n */\r\nexport class RestricitonsEpics {\r\n\r\n  /**\r\n     *Creates an instance of RestricitonsEpics.\r\n     * @param {string} [restrictionModalId] Element id of the restrictions dialog modal view\r\n     * @memberof RestricitonsEpics\r\n     */\r\n  constructor(private client: BaseClient, private restrictionModalId?: string) { }\r\n\r\n  combineEpics(): any {\r\n    return combineEpics(\r\n      this.raiseRestrictionEpic,\r\n      this.fadeRestrictionEpic,\r\n      this.restrictionActionsEpic\r\n    );\r\n  }\r\n\r\n  private get raiseRestrictionEpic(): RestricitonsEpic {\r\n    return (action$) =>\r\n      action$.pipe(\r\n        ofType(raiseRestriction.toString()),\r\n        mergeMap(() => {\r\n        // Side effect\r\n          Utils.showLightbox(this.restrictionModalId || 'RESTRICTIONS_MODAL');\r\n          return EMPTY; // no further actions\r\n        })\r\n      );\r\n  }\r\n\r\n  private get fadeRestrictionEpic(): RestricitonsEpic {\r\n    return (action$) =>\r\n      action$.pipe(\r\n        ofType(acceptRestriction.toString(), declineRestriction.toString()),\r\n        mergeMap(() => {\r\n        // Side effect\r\n          Utils.hideLightbox(this.restrictionModalId || 'RESTRICTIONS_MODAL');\r\n          return EMPTY; // no further actions\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n     * Run this when restriction is accepted in lightbox\r\n     * it should either redirect user to wherever redirectURLKey\r\n     * is pointing to, or run whatever action required (not implements)\r\n     * @readonly\r\n     * @private\r\n     * @type {NavigationEpic}\r\n     * @memberof NavigationEpics\r\n     */\r\n  private get restrictionActionsEpic(): RestricitonsEpic {\r\n    return (action$) =>\r\n      action$.pipe(\r\n        ofType(\r\n          Actions.acceptRestriction.toString(),\r\n          Actions.declineRestriction.toString()\r\n        ),\r\n        filter(({ payload }: Action<Volt.IHypermediaAction>) => Boolean(payload)),\r\n        mergeMap(({ payload }: Action<Volt.IHypermediaAction>) => {\r\n          if (payload.redirectURLKey) {\r\n          // Do the redirection whenever restriction action has a redirectURLKey\r\n            return of(broadcastUpdate(historyGo(payload.redirectURLKey)));\r\n          }\r\n\r\n          if (payload.href) {\r\n          // Run restriction action when available\r\n            return concat(\r\n              of(setWidgetStatus(EWidgetStatus.UPDATING)),\r\n              this.client.action<AjaxResponse<any>>(payload).pipe(\r\n                mergeMap((response) =>\r\n                  FilterRestrictionObservable(response, [\r\n                    ValueOf(\r\n                      response,\r\n                      'data.productOfferingDetail.redirectURLKey',\r\n                      false\r\n                    )\r\n                      ? historyGo(\r\n                        ValueOf(\r\n                          response,\r\n                          'data.productOfferingDetail.redirectURLKey'\r\n                        )\r\n                      )\r\n                      : finalizeRestriction(response.data)\r\n                  ])\r\n                )\r\n              )\r\n            );\r\n          }\r\n\r\n          // Just dismiss\r\n          return of();\r\n        }),\r\n        catchError(Models.ErrorHandlerObservable(Actions.acceptRestriction))\r\n      );\r\n  }\r\n\r\n}\r\n\r\ntype RestricitonsEpic = Epic<ReduxActions.Action<any>, any>;\r\n", "import { Epic, combineEpics, ofType } from \"redux-observable\";\r\nimport { Actions } from \"../Actions\";\r\nimport { ILightboxPayload } from \"../Models\";\r\nimport { Utils } from \"../Utils\";\r\nimport { filter, mergeMap, tap } from \"rxjs\";\r\n\r\nconst {\r\n  openLightbox,\r\n  closeLightbox,\r\n  setlightboxData\r\n} = Actions;\r\n\r\n/**\r\n * Basic widget lifecycle events\r\n * * Status update\r\n * * Error handeling\r\n * @export\r\n * @class ModalEpics\r\n */\r\nexport class ModalEpics {\r\n  combineEpics(): any {\r\n    return combineEpics(\r\n      this.onOpenLightboxEpic,\r\n      this.closeLightbox\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Epic activated on open lightbox action\r\n   *\r\n   * @readonly\r\n   * @private\r\n   * @type {GeneralEpic}\r\n   * @memberof Epics\r\n   * @requires lightboxData store entry in widget\r\n   */\r\n  private get onOpenLightboxEpic(): GeneralEpic {\r\n    return (action$) =>\r\n      action$.pipe(\r\n        ofType(openLightbox.toString()),\r\n        filter(({ payload }: ReduxActions.Action<ILightboxPayload>) =>\r\n          !Utils.isLightboxOpen(\r\n            typeof payload === 'string' ? payload : payload.lightboxId\r\n          )\r\n        ),\r\n        tap(({ payload }: ReduxActions.Action<ILightboxPayload | string>) =>\r\n          Utils.showLightbox(\r\n            typeof payload === 'string' ? payload : payload.lightboxId\r\n          )\r\n        ),\r\n        mergeMap(({ payload }: ReduxActions.Action<ILightboxPayload>) => [\r\n          setlightboxData(\r\n            typeof payload === 'string' ? payload : payload.data\r\n          )\r\n        ])\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Epic activated on open lightbox action\r\n   *\r\n   * @readonly\r\n   * @private\r\n   * @type {GeneralEpic}\r\n   * @memberof Epics\r\n   * @requires lightboxData store entry in widget\r\n   */\r\n  private get closeLightbox(): GeneralEpic {\r\n    return (action$) =>\r\n      action$.pipe(\r\n        ofType(closeLightbox.toString()),\r\n        filter(({ payload }: ReduxActions.Action<ILightboxPayload>) =>\r\n          Utils.isLightboxOpen(\r\n            typeof payload === 'string' ? payload : payload.lightboxId\r\n          )\r\n        ),\r\n        tap(({ payload }: ReduxActions.Action<ILightboxPayload>) =>\r\n          Utils.hideLightbox(\r\n            typeof payload === 'string' ? payload : payload.lightboxId\r\n          )\r\n        )\r\n      );\r\n  }\r\n\r\n}\r\n\r\ntype GeneralEpic = Epic<ReduxActions.Action<any>, any>;\r\n", "/*\r\n * External Epics:\r\n * collection of general purpose sharable epics\r\n * to be added to individual widgets whenever\r\n * required.\r\n *\r\n * The implementation is as follows:\r\n * src/Strore/Store.ts\r\n *\r\n * @Injectable\r\n * export class Store extends BaseStore<IStoreState> {\r\n *     ...\r\n *   get middlewares() {\r\n *     return [\r\n *       createEpicMiddleware(this.epics.combineEpics()),\r\n *       ...,\r\n *       createEpicMiddleware(new LifecycleEpics().combineEpics())\r\n *     ];\r\n *   }\r\n * }\r\n * NOTE:\r\n * Middlewares are collected bottom-to-top\r\n * so, the bottom-most epic will receive the\r\n * action first, while the top-most -- last\r\n */\r\nexport * from \"./Lifecycle\";\r\nexport * from \"./Restrictions\";\r\nexport * from \"./Modals\";\r\n", "import { ReducersMapObject } from \"redux\";\r\nimport { handleActions, Action } from \"redux-actions\";\r\nimport { CommonFeatures } from \"bwtk\";\r\nimport { Actions } from \"../Actions\";\r\nimport { EWidgetStatus } from \"../Models\";\r\n\r\nconst { actionsToComputedPropertyName } = CommonFeatures;\r\nconst {\r\n  setWidgetStatus,\r\n  errorOccured,\r\n} = actionsToComputedPropertyName(Actions);\r\nexport function getWidgetBaseLifecycle(localization: any): ReducersMapObject {\r\n  return {\r\n    // =========== Widget state =============\r\n    // Set localization inherited from widget props onto the store\r\n    localization: localization.createReducer(),\r\n    // Set widget status\r\n    widgetStatus: handleActions<EWidgetStatus>({\r\n      [setWidgetStatus]: (state, { payload }: Action<EWidgetStatus>) => payload\r\n    }, EWidgetStatus.UPDATING),\r\n    // Set any runtime errors\r\n    error: handleActions<any | null>({\r\n      [errorOccured]: (state, { payload }: Action<any>) => payload\r\n    }, null)\r\n  };\r\n}\r\n", "import { ReducersMapObject } from \"redux\";\r\nimport { handleActions } from \"redux-actions\";\r\nimport { CommonFeatures } from \"bwtk\";\r\nimport { Actions } from \"../Actions\";\r\nimport { Volt } from \"../Models\";\r\n\r\nconst { actionsToComputedPropertyName } = CommonFeatures;\r\nconst {\r\n  raiseRestriction,\r\n  acceptRestriction,\r\n  declineRestriction\r\n} = actionsToComputedPropertyName(Actions);\r\nexport function getWidgetRestrictions(): ReducersMapObject {\r\n  return {\r\n    restriction: handleActions<Volt.IRestriction | null>({\r\n      [raiseRestriction]: (state, {payload}) => payload || state,\r\n      [acceptRestriction]: () => null,\r\n      [declineRestriction]: () => null\r\n    }, null) as any\r\n  };\r\n}\r\n", "import { ReducersMapObject } from \"redux\";\r\nimport { handleActions } from \"redux-actions\";\r\nimport { CommonFeatures } from \"bwtk\";\r\nimport { Actions } from \"../Actions\";\r\n\r\nconst { actionsToComputedPropertyName } = CommonFeatures;\r\n\r\nconst {\r\n  closeLightbox,\r\n  setlightboxData\r\n} = actionsToComputedPropertyName(Actions);\r\nexport function getWidgetLightboxes(): ReducersMapObject {\r\n  return {\r\n    lightboxData: handleActions<any | null>({\r\n      [setlightboxData]: (state, { payload }) => payload,\r\n      [closeLightbox]: () => null\r\n    }, null)\r\n  };\r\n}\r\n", "import { getWidgetBaseLifecycle } from \"./Lifecycle\";\r\nimport { getWidgetRestrictions } from \"./Restrictions\";\r\nimport { getWidgetLightboxes } from \"./Modals\";\r\n\r\nexport namespace Reducers {\r\n  export const WidgetBaseLifecycle: typeof getWidgetBaseLifecycle = getWidgetBaseLifecycle;\r\n  export const WidgetRestrictions: typeof getWidgetRestrictions = getWidgetRestrictions;\r\n  export const WidgetLightboxes: typeof getWidgetLightboxes = getWidgetLightboxes;\r\n}\r\n", "import * as React from \"react\";\r\nimport { Models } from \"../../Models\";\r\n\r\nexport interface IPanelComponentProps extends Models.IBaseComponentProps {\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport interface IPanelComponent extends React.FC<IPanelComponentProps> {\r\n}\r\n\r\n\r\nexport const PanelComponent: IPanelComponent = ({\r\n  className, children\r\n}) => <div className={`brf3-panel ${className}`}>{children}</div>;\r\n\r\nPanelComponent.defaultProps = {\r\n  className: \"\",\r\n};\r\n", "import * as React from \"react\";\r\nimport { Models } from \"../../Models\";\r\n\r\nexport interface IBRF3ContainerProps extends Models.IBaseComponentProps, React.PropsWithChildren {\r\n}\r\n\r\nexport interface IBRF3ContainerComponent extends React.FC<IBRF3ContainerProps> {\r\n}\r\n\r\n\r\nexport const BRF3ContainerComponenet: IBRF3ContainerComponent = ({\r\n  className, children\r\n}) => <div className={`brf3-container ${className}`}>{children}</div>;\r\n\r\nBRF3ContainerComponenet.defaultProps = {\r\n  className: \"\",\r\n};\r\n", "import * as React from \"react\";\r\nimport { Models } from \"../../Models\";\r\n\r\nexport interface IContainerComponentProps extends Models.IBaseComponentProps {\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport interface IContainerComponent extends React.FC<IContainerComponentProps> {\r\n}\r\n\r\n\r\nexport const ContainerComponent: IContainerComponent = ({\r\n  className, children\r\n}) => <div className={`container liquid-container ${className}`}>{children}</div>;\r\n\r\nContainerComponent.defaultProps = {\r\n  className: \"\"\r\n};\r\n\r\nexport * from \"./Panel\";\r\nexport * from \"./BRF3Container\";\r\n", "import { Omniture } from \".\";\r\n\r\nexport declare class OmnitureTracker {\r\n  history: Array<Omniture.IProps>;\r\n  static getInstance(): OmnitureTracker;\r\n  constructor();\r\n  private _track(props, isPage?);\r\n  trackPage(props: Omniture.IProps): void;\r\n  trackFragment(props: Omniture.IProps): void;\r\n  trackAction(props: Omniture.IProps): void;\r\n  trackError(err: Omniture.IError | Array<Omniture.IError>, actionId?: string | number): void;\r\n  trackFailure(err: any): void;\r\n  updateContext(props: Omniture.IOmniture): void;\r\n}\r\n", "import * as React from \"react\";\r\nimport { OmnitureTracker } from \"./Tracker\";\r\n\r\nexport * from \"./Tracker\";\r\n\r\nexport namespace Omniture {\r\n\r\n  export interface IOmniture {\r\n    // Omniture specific\r\n    s_oPGN?: string | \"~\" | IElementReffrence;\r\n    s_oLNG?: string | \"~\" | IElementReffrence;\r\n    s_oPRV?: string | \"~\" | IElementReffrence;\r\n    s_oSS1?: string | \"~\" | IElementReffrence;\r\n    s_oSS2?: string | \"~\" | IElementReffrence;\r\n    s_oSS3?: string | \"~\" | IElementReffrence;\r\n    s_oLGS?: boolean;\r\n    s_oSID?: string | \"~\" | IElementReffrence;\r\n    s_oPTE?: boolean;\r\n    s_oLOB?: string | \"~\" | IElementReffrence;\r\n    s_oACT?: string | \"~\" | IElementReffrence;\r\n    s_oMOT?: string | \"~\" | IElementReffrence;\r\n    s_oBUP?: string | \"~\" | IElementReffrence;\r\n    s_oMOID?: string | \"~\" | IElementReffrence;\r\n    s_oIID?: string | \"~\" | IElementReffrence;\r\n    s_oRID?: string | \"~\" | IElementReffrence;\r\n    s_oESTD?: string | \"~\" | IElementReffrence;\r\n    s_oESTT?: string | \"~\" | IElementReffrence;\r\n    s_oAPT?: \"~\" | IApplicationState;\r\n    s_oPRM?: string | \"~\" | IElementReffrence;\r\n    s_oLBC?: string | \"~\" | IElementReffrence;\r\n    s_oPLE?: \"*\" | IMessage | IElementReffrence | Array<IMessage | IElementReffrence>;\r\n    s_oARS?: \"*\" | string | IElementReffrence | Array<string | IElementReffrence>;\r\n    s_oERR_CLASS?: \"*\" | IErrorClass | IElementReffrence | Array<string | IErrorClass | IElementReffrence>;\r\n    s_oERR_DESC?: \"*\" | IErrorDescription | IElementReffrence | Array<string | IErrorDescription | IElementReffrence>;\r\n    s_oAJC?: boolean;\r\n    // combination of s_oARS + s_oERR_CLASS + s_oERR_DESC + s_oPLE +? s_oAJC\r\n    // s_oERR?: \"*\" | IError | Array<IError | IElementReffrence>;\r\n    // --\r\n    s_oBTN?: string | IElementReffrence;\r\n    s_oEPN?: string | IElementReffrence;\r\n    s_oCOSL?: string | IElementReffrence;\r\n    s_oPRD?: \"*\" | IProduct | Array<IProduct>;\r\n    s_oPID?: string | IElementReffrence;\r\n    s_oSRT?: string | IElementReffrence;\r\n  }\r\n  /**\r\n     * Omniture component props\r\n     * @export\r\n     * @interface IProps\r\n     * @namespace Omniture\r\n     */\r\n  export interface IProps extends IOmniture {\r\n    id: string;\r\n    // Engine flags\r\n    rel?: \"page\" | \"fragment\" | \"component\";\r\n    ready?: boolean;\r\n    enabled?: boolean;\r\n    once?: boolean;\r\n    // collect?: Array<string>;\r\n    // parent?: \"\";\r\n    timestamp?: string;\r\n  }\r\n\r\n  export interface IElementReffrence {\r\n    // DOM element ID\r\n    ref: string;\r\n    // Max characters\r\n    maxlength?: number;\r\n    // Regular expression to apply\r\n    regex?: string;\r\n  }\r\n\r\n  export function Ref(elementId: string): IElementReffrence {\r\n    return {\r\n      ref: elementId\r\n    };\r\n  }\r\n\r\n  export interface IApplicationState {\r\n    actionId?: string | number;\r\n    actionresult?: string | number;\r\n    applicationState?: string | number;\r\n  }\r\n\r\n  export interface IMessage {\r\n    type: EMessageType;\r\n    content: string | IElementReffrence;\r\n    errorCodes?: Array<string>;\r\n  }\r\n\r\n  export interface IProduct {\r\n    category: string;\r\n    name: string;\r\n    sku: string;\r\n    quantity: string;\r\n    price: string;\r\n    promo: string;\r\n  }\r\n\r\n  export enum EMessageType {\r\n    Confirmation = \"C\",\r\n    Information = \"I\",\r\n    Warning = \"W\",\r\n    Error = \"E\",\r\n  }\r\n\r\n  export enum EErrorType {\r\n    Technical = \"T\",\r\n    Business = \"B\",\r\n    Validation = \"V\"\r\n  }\r\n\r\n  export enum EApplicationLayer {\r\n    Browser = \"BR\",\r\n    Frontend = \"FE\",\r\n    ESB = \"ESB\",\r\n    Backend = \"BE\",\r\n    Servicegrid = \"SG\",\r\n    Cache = \"C\"\r\n  }\r\n\r\n  export interface IErrorDescription {\r\n    code: string | number;\r\n    description: string | IElementReffrence;\r\n  }\r\n\r\n  export interface IErrorClass {\r\n    code: string | number;\r\n    type: EErrorType;\r\n    layer: EApplicationLayer;\r\n  }\r\n\r\n  export interface IError extends IErrorClass, IErrorDescription {\r\n    ajax?: boolean;\r\n    lightbox?: {\r\n      title?: string | IElementReffrence,\r\n      content?: string | IElementReffrence\r\n    };\r\n  }\r\n\r\n  /**\r\n     * Omniture wrapper component\r\n     * @param {IProps} props\r\n     * @returns React.FC\r\n     * @namespace Omniture\r\n     */\r\n  export const Component: React.FC<React.PropsWithChildren<IOmniture>> = (props) => {\r\n    const {\r\n      children,\r\n      ...omniture\r\n    } = props;\r\n    const _childern = React.Children.toArray(children);\r\n    if (_childern.length > 1) throw \"Omniture component may not have more then one child\";\r\n    return _childern.length > 0 ? <>\r\n      {\r\n        _childern.map((el: any) => React.cloneElement(el, { ...el.props, \"data-omni\": btoa(JSON.stringify(omniture)) }))\r\n      }\r\n    </> : <span data-omni={btoa(JSON.stringify(omniture))} />;\r\n  };\r\n\r\n  const NOOPE = () => { };\r\n  const _stub = {\r\n    trackPage: NOOPE,\r\n    trackFragment: NOOPE,\r\n    trackAction: NOOPE,\r\n    trackError: NOOPE,\r\n    trackFailure: NOOPE,\r\n    updateContext: NOOPE,\r\n  };\r\n\r\n  export function useOmniture(): OmnitureTracker {\r\n    return ((window as any)[\"OmnitureTracker\"] && (window as any)[\"OmnitureTracker\"].getInstance()) || _stub;\r\n  }\r\n}\r\n", "import * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { ContainerComponent, PanelComponent } from \"../Container\";\r\nimport { Models, EFlowType } from \"../../Models\";\r\nimport { Omniture } from \"../../Omniture\";\r\nimport { ValueOf, Utils } from \"../../Utils\";\r\n\r\nexport interface IErrorComponentProps {\r\n  details: Models.ErrorHandler;\r\n}\r\n\r\nexport interface IErrorComponent extends React.FC<IErrorComponentProps> {\r\n}\r\n\r\nexport const ErrorComponent: IErrorComponent = ({ details }) => {\r\n  React.useEffect(() => {\r\n    let action = 0;\r\n    switch (Utils.getFlowType()) {\r\n      case EFlowType.INTERNET:\r\n        action = 543;\r\n        break;\r\n      case EFlowType.TV:\r\n        action = 394;\r\n        break;\r\n    }\r\n    if (details.response ? details.response.url.includes(\"ProductOrder/Summary\") : 0)\r\n      action = 104;\r\n    switch (details.type) {\r\n      case \"API\":\r\n        Omniture.useOmniture().trackError({\r\n          code: \"API\" + ValueOf(details, \"response.status\", \"500\"),\r\n          type: Omniture.EErrorType.Technical,\r\n          layer: Omniture.EApplicationLayer.Backend,\r\n          description: {\r\n            ref: \"TechnicalErrorMessage\"\r\n          },\r\n          ajax: true\r\n        }, action);\r\n        break;\r\n      case \"widget\":\r\n        Omniture.useOmniture().trackError({\r\n          code: \"WIDGET400\",\r\n          type: Omniture.EErrorType.Technical,\r\n          layer: Omniture.EApplicationLayer.Frontend,\r\n          description: {\r\n            ref: \"TechnicalErrorMessage\"\r\n          }\r\n        }, action);\r\n        break;\r\n      case \"logic\":\r\n      default:\r\n        Omniture.useOmniture().trackError({\r\n          code: \"LOGIC500\",\r\n          type: Omniture.EErrorType.Technical,\r\n          layer: Omniture.EApplicationLayer.Frontend,\r\n          description: {\r\n            ref: \"TechnicalErrorMessage\"\r\n          }\r\n        }, action);\r\n        break;\r\n    }\r\n  }, []);\r\n  return <ContainerComponent className=\"error margin-30-bottom\">\r\n    <div className=\"spacer30\" aria-hidden=\"true\" />\r\n    <PanelComponent className=\"border bgWgite borderGray4 pad-30\">\r\n      <div className=\"row\">\r\n        <div className=\"inlineBlock icon-width-40 valign-middle text-center-xs\">\r\n          <span className=\"txtRed txtSize32 icons icons-info\" />\r\n        </div>\r\n        <div className=\"spacer15\" aria-hidden=\"true\" />\r\n        <div className=\"inlineBlock pad-20-left no-pad-left-xs content-width valign-middle\">\r\n          <span className=\"txtBlack2 block txtSize20\" id=\"TechnicalErrorMessage\">\r\n            <FormattedMessage id=\"TECHNICAL_ERROR\" />\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <div className=\"margin-20-top\" style={{ display: details.debug ? \"block\" : \"none\" }}>\r\n        {\r\n          ((type) => {\r\n            switch (type) {\r\n              case \"API\":\r\n                return <React.Fragment>\r\n                  <p className=\"margin-10-top\">API Request failed {ValueOf(details, \"response.status\", \"unknown\")} ({ValueOf(details, \"response.statusText\", \"unknown\")})</p>\r\n                  <p className=\"margin-10-top\" style={{ wordBreak: \"break-all\" }}>URL: {ValueOf(details, \"response.url\", \"unknown\")}</p>\r\n                  <p className=\"margin-10-top\" style={{ wordBreak: \"break-all\" }}>Response: {JSON.stringify(ValueOf(details, \"response.data\", \"Null\"), null, \" \")}</p>\r\n                </React.Fragment>;\r\n              case \"widget\":\r\n                return <React.Fragment>\r\n                  <p className=\"margin-10-top\">Widget render failed</p>\r\n                  <p className=\"margin-10-top\">Component: <pre>{details.componentStack}</pre></p>\r\n                </React.Fragment>;\r\n              case \"logic\":\r\n              default:\r\n                return <p className=\"margin-10-top\">General logic falure</p>;\r\n            }\r\n          })(details.type)\r\n        }\r\n        <p className=\"margin-10-top\">Stack trace: <pre>{JSON.stringify(ValueOf(details, \"stack\"), null, \" \")}</pre></p>\r\n      </div>\r\n    </PanelComponent>\r\n  </ContainerComponent>;\r\n};\r\n", "\r\nexport interface IVisibleProps extends React.PropsWithChildren {\r\n  when: boolean;\r\n  placeholder?: any;\r\n}\r\n\r\nexport /**\r\n * Conditional visibility container component\r\n * returns component children is [when] prop\r\n * evaluates to true. Otherwise returns [placeholder/null]\r\n * @param {boolean} when - test statement\r\n * @param {any} [placeholder] - placeholder component (optional)\r\n */\r\nconst VisibleComponent: React.FC<IVisibleProps> = (props) =>\r\n  (typeof props.when === \"boolean\" ? props.when : <PERSON><PERSON><PERSON>(props.when)) ?\r\n    props.children :\r\n    props.placeholder;\r\n\r\n", "import * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { Actions } from \"../../Actions\";\r\nimport { Models } from \"../../Models\";\r\nimport { Utils } from \"../../Utils\";\r\nimport { VisibleComponent } from \"../VisibilityContainer\";\r\n\r\ndeclare const $: (t: string) => any;\r\nenum EModalEvent {\r\n  SHOW = \"show.bs.modal\",\r\n  SHOWN = \"shown.bs.modal\",\r\n  HIDE = \"hide.bs.modal\",\r\n  HIDDEN = \"hidden.bs.modal\"\r\n}\r\n\r\nexport interface ILightboxContainerProps extends Models.IBaseComponentProps {\r\n  title: any;\r\n  modalId: string;\r\n  flexDisplay?: boolean;\r\n  size?: string;\r\n  permanent?: boolean;\r\n  containerClass?: Array<string>;\r\n  onShow?: () => void;\r\n  onShown?: () => void;\r\n  onHide?: () => void;\r\n  onHidden?: () => void;\r\n  onClose?: Function | undefined;\r\n  onDismiss?: () => void;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport interface ILightboxConnectedProps {\r\n  lightboxData?: any;\r\n}\r\n\r\nexport interface ILightboxContainerDispatches {\r\n  onCloseLightbox: (modalId: string) => void;\r\n  clearLightboxData: () => void;\r\n}\r\n\r\nexport class Component extends React.Component<ILightboxContainerProps & ILightboxConnectedProps & ILightboxContainerDispatches> {\r\n  static defaultProps = {\r\n    className: \"\",\r\n    size: \"md\"\r\n  };\r\n  componentDidMount() {\r\n    this.props.onShow &&\r\n      $(`#${this.props.id || this.props.modalId}`).on(EModalEvent.SHOW, this.props.onShow);\r\n    this.props.onShown &&\r\n      $(`#${this.props.id || this.props.modalId}`).on(EModalEvent.SHOWN, this.props.onShown);\r\n    this.props.onHide &&\r\n      $(`#${this.props.id || this.props.modalId}`).on(EModalEvent.HIDE, this.props.onHide);\r\n    this.props.onHidden &&\r\n      $(`#${this.props.id || this.props.modalId}`).on(EModalEvent.HIDDEN, this.props.onHidden);\r\n    this.onClose = this.onClose.bind(this);\r\n    // Move focus back to open button element\r\n    $(`#${this.props.id || this.props.modalId}`).on(EModalEvent.HIDDEN, () => {\r\n      const domElem = this.props.lightboxData && this.props.lightboxData.relativeId && document.getElementById(this.props.lightboxData.relativeId);\r\n      domElem && domElem.focus();\r\n      this.props.clearLightboxData();\r\n    }\r\n    );\r\n  }\r\n\r\n  onClose() {\r\n    if (Utils.isLightboxOpen(this.props.id || this.props.modalId)) {\r\n      this.props.onCloseLightbox(this.props.id || this.props.modalId);\r\n      this.props.onClose !== undefined && this.props.onClose(this.props.id || this.props.modalId);\r\n    }\r\n  }\r\n\r\n  render() {\r\n    const {\r\n      id, className = \"\", size,\r\n      title, containerClass = [],\r\n      modalId, children, onDismiss,\r\n      permanent\r\n    } = this.props;\r\n    return <div\r\n      id={id || modalId}\r\n      className={`modal modal-vm fade ${className}`}\r\n      role=\"dialog\"\r\n      tabIndex={-1}\r\n      data-backdrop=\"static\"\r\n      data-keyboard=\"false\"\r\n      aria-modal=\"true\"\r\n      aria-labelledby={`${id || modalId}_label`}\r\n      // aria-describedby={`${id || modalId}_desc`}\r\n      aria-hidden=\"true\"\r\n    >\r\n      <span className=\"sr-only\">dialog</span>\r\n      <div className={`modal-dialog modal-md modal-bg modal-${size} bell-modal-${size}`} role=\"document\">\r\n        <div className=\"modal-content noBorderRadius noBorder-xs\">\r\n          <div className=\"modal-header bgGrayLight2 pad-30-left pad-30-right pad-25-top pad-25-bottom pad-15-left-right-xs align-items-center noBorderRadius accss-focus-outline-override-grey-bg\">\r\n            <h2 id={`${id || modalId}_label`} className=\"virginUltra txtBlack txtSize24 overflow-ellipsis txtSize18-xs txtUppercase sans-serif-xs lineHeight1_5 margin-b-0\">{title}</h2>\r\n            <VisibleComponent when={!permanent}>\r\n              <button onClick={onDismiss} id={`close_${id || modalId}`} type=\"button\" className=\"no-pad close\" data-dismiss=\"modal\" aria-label=\"Close Dialog\" aria-describedby={`${id || modalId}_label`} autoFocus={true}><span className=\"volt-icon icon-big_X\"></span></button>\r\n            </VisibleComponent>\r\n          </div>\r\n          <div id={`${id || modalId}_desc`} className={`modal-body pad-0 ${containerClass.join(\" \")}`}>\r\n            {children}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>;\r\n  }\r\n  componentWillUnmount() {\r\n    this.props.onShow &&\r\n      $(`#${this.props.id || this.props.modalId}`).off(EModalEvent.SHOW, this.props.onShow);\r\n    this.props.onShown &&\r\n      $(`#${this.props.id || this.props.modalId}`).off(EModalEvent.SHOWN, this.props.onShown);\r\n    this.props.onHide &&\r\n      $(`#${this.props.id || this.props.modalId}`).off(EModalEvent.HIDE, this.props.onHide);\r\n    this.props.onHidden &&\r\n      $(`#${this.props.id || this.props.modalId}`).off(EModalEvent.HIDDEN, this.props.onHidden);\r\n  }\r\n}\r\n\r\nexport const LightboxContainer = connect<{}, ILightboxContainerDispatches, ILightboxContainerProps>(\r\n  ({ lightboxData }: any) => ({ lightboxData }),\r\n  (dispatch) => ({\r\n    onCloseLightbox: (modalId: string) => dispatch(Actions.closeLightbox(modalId)),\r\n    clearLightboxData: () => dispatch(Actions.setlightboxData(\"\"))\r\n  })\r\n)(Component);\r\n", "// X.X - Lightbox - Restriction\r\n// https://projects.invisionapp.com/d/main#/console/14385632/305962610/preview\r\nimport * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { Actions } from \"../../Actions\";\r\nimport { FormattedMessage, FormattedDate } from \"react-intl\";\r\nimport { Volt } from \"../../Models\";\r\nimport { Omniture } from \"../../Omniture\";\r\nimport { ValueOf } from \"../../Utils\";\r\nimport { LightboxContainer } from \"../Lightbox\";\r\nimport { VisibleComponent } from \"../VisibilityContainer\";\r\n\r\nexport interface IComponentProps {\r\n  id?: string;\r\n}\r\n\r\nexport interface IComponentConnectedProps extends Volt.IRestriction {\r\n  onComplete?: any;\r\n}\r\n\r\nexport interface IComponentDispatches {\r\n  onAction: (button: Volt.IHypermediaAction) => void;\r\n  onDismiss: () => void;\r\n}\r\n\r\nfunction getButtonClass(i: number) {\r\n  switch (true) {\r\n    case i === 0: return \"btn btn-primary fill-xs\";\r\n    case i === 1: return \"btn btn-default fill-xs\";\r\n    default: return \"btn btn-link\";\r\n  }\r\n}\r\n\r\nfunction getMessageIcon(type: string) {\r\n  switch (type) {\r\n    case \"Error\": return <span className=\"icon2 icon-alert-circled txtSize38 txtRed pad-15-right\" />;\r\n    case \"Information\": return <span className=\"icon2 icon-alert-circled txtSize38 txtYellow pad-15-right\" />;\r\n    case \"Warning\": return <span className=\"icon2 icon-alert-circled txtSize38 txtYellow pad-15-right\" />;\r\n    default: return null;\r\n  }\r\n}\r\n\r\nlet lightboxType: string = \"\";\r\nfunction onShowOmniture(id: string) {\r\n  let messageType;\r\n  const s_oAPT = {\r\n    actionId: 104,\r\n    actionresult: 0,\r\n    applicationState: 0\r\n  };\r\n  switch (lightboxType) {\r\n    case \"Warning\":\r\n    case \"Error\":\r\n      s_oAPT.actionresult = 2;\r\n      s_oAPT.applicationState = 2;\r\n      messageType = Omniture.EMessageType.Warning;\r\n      break;\r\n    case \"Information\":\r\n    default:\r\n      messageType = Omniture.EMessageType.Information;\r\n  }\r\n  Omniture.useOmniture().trackFragment({\r\n    id: \"restrictionLightbox\",\r\n    s_oAPT,\r\n    s_oPRM: {\r\n      ref: `${id}_label`\r\n    },\r\n    s_oLBC: {\r\n      ref: `${id}_description`\r\n    },\r\n    s_oPLE: {\r\n      content: {\r\n        ref: `${id}_description`\r\n      },\r\n      type: messageType\r\n    }\r\n  });\r\n};\r\n\r\nconst Component: React.FC<IComponentProps & IComponentConnectedProps & IComponentDispatches> = ({\r\n  id,\r\n  type,\r\n  title,\r\n  description,\r\n  dynamicData,\r\n  footerDescription,\r\n  actionLinks,\r\n  onDismiss,\r\n  onAction,\r\n  onComplete\r\n}) => {\r\n  lightboxType = type;\r\n  return <LightboxContainer modalId={id || \"RESTRICTIONS_MODAL\"} permanent={true}\r\n    onClose={() => { onDismiss(); onComplete && onComplete(\"close\"); }}\r\n    onShown={() => onShowOmniture(id as string)}\r\n    title={title}>\r\n    <div className=\"modal-body bgWhite\">\r\n      <div className=\"flex\">\r\n        {getMessageIcon(type || \"\")}\r\n        <div id={`${id}_description`}>\r\n          <p dangerouslySetInnerHTML={{ __html: description }} />\r\n          <VisibleComponent when={ValueOf(dynamicData, \"productList.length\", false)}>\r\n            <ul>\r\n              {\r\n                ValueOf(dynamicData, \"productList\", []).map((product: string) => <li className=\"txtBold txtBlack\">{product}</li>)\r\n              }\r\n            </ul>\r\n          </VisibleComponent>\r\n          <VisibleComponent when={ValueOf(dynamicData, \"promotion.length\", false)}>\r\n            <ul>\r\n              {\r\n                ValueOf(dynamicData, \"promotion\", []).map((promo: any) => <li>\r\n                  <span className=\"txtBold txtBlack\">{promo.promoName}</span><br />\r\n                  <VisibleComponent when={Boolean(promo.promoExpiry)}>\r\n                    <FormattedDate value={promo.promoExpiry} format=\"yMMMd\" timeZone=\"UTC\">\r\n                      {(expiryDate: any) => <FormattedMessage id=\"PromotionExpires\" values={{ expiryDate }} />}\r\n                    </FormattedDate>\r\n                  </VisibleComponent>\r\n                </li>)\r\n              }\r\n            </ul>\r\n          </VisibleComponent>\r\n          <VisibleComponent when={Boolean(footerDescription)}>\r\n            <p className=\"txtBold\" dangerouslySetInnerHTML={{ __html: footerDescription }} />\r\n          </VisibleComponent>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <VisibleComponent when={Boolean(actionLinks && actionLinks.length > 0)}>\r\n      <div className=\"spacer1 bgGrayLight6\" aria-hidden=\"true\"></div>\r\n      <div className=\"bgGray19 pad-30 pad-15-left-right-xs\">\r\n        {\r\n          ValueOf<Array<Volt.IHypermediaAction>>(actionLinks, undefined, [])\r\n            .map((action, i) => <React.Fragment>\r\n              <button id={`ACTION_SUBMIT`} className={getButtonClass(i)} onClick={() => { onAction(action); onComplete && onComplete(action.rel); }}>{action.name}</button>\r\n              <div className=\"vSpacer15\" aria-hidden=\"true\"></div>\r\n            </React.Fragment>)\r\n        }\r\n      </div>\r\n    </VisibleComponent>\r\n  </LightboxContainer>;\r\n};\r\n\r\nexport const RestrictionModalView = connect<IComponentConnectedProps, IComponentDispatches, IComponentProps>(\r\n  ({ restriction }: any) => (restriction ? { ...restriction } : {}) as IComponentConnectedProps,\r\n  (dispatch) => ({\r\n    onAction: (action) => {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"restrictionLightbox\",\r\n        s_oAPT: {\r\n          actionId: 647,\r\n          actionresult: 0,\r\n          applicationState: 0\r\n        },\r\n        s_oBTN: action.name\r\n      });\r\n      switch (action.name) {\r\n        case \"Cancel\": dispatch(Actions.declineRestriction(action)); break;\r\n        default: dispatch(Actions.acceptRestriction(action));\r\n      }\r\n    },\r\n    onDismiss: () => dispatch(Actions.declineRestriction())\r\n  })\r\n)(Component);\r\n", "import * as React from \"react\";\r\n\r\nexport interface IProps {\r\n  text: string;\r\n  maxLength: number;\r\n  className?: string;\r\n}\r\n\r\nexport interface IEllipsisText extends React.FC<IProps> {}\r\n\r\nexport const EllipsisText: IEllipsisText = (props: any) => {\r\n  const { text, maxLength, className } = props;\r\n\r\n  return <p className={`ellipsis-text ${className}`}>{text.length <= maxLength ? text : `${text.substring(0, maxLength)}...`}</p>;\r\n};\r\n\r\nEllipsisText.defaultProps = {\r\n  className: \"\"\r\n};\r\n\r\nEllipsisText.displayName = \"EllipsisText\";\r\n", "import * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { IntlProvider } from \"react-intl\";\r\nimport { Models, EWidgetStatus } from \"../../Models\";\r\nimport { ErrorComponent } from \"../Error\";\r\n\r\nexport interface IApplicationRootProps {\r\n  propsfilter?: (props: Models.IBaseAppProps) => Models.IBaseAppProps;\r\n  placeholder?: any | null;\r\n  children?: any;\r\n}\r\n\r\n/**\r\n * Main application view router\r\n * displays one of the 3 top level views\r\n * * null view: no data yet, there is nothing to show\r\n * * ErrorHandler: application failed\r\n * * Application: the app itself\r\n * @param {*} props\r\n * @returns\r\n */\r\nconst Component: React.FC<Models.IBaseAppProps & IApplicationRootProps> = (props) => (\r\n  Boolean(props.localization &&\r\n        props.localization.messages &&\r\n        Object.keys(props.localization.messages).length) ?\r\n    <IntlProvider {...props.localization} formats={{ ...props.localization.formats, number: { CAD: { currency: \"CAD\", currencyDisplay: \"symbol\", style: \"currency\", minimumFractionDigits: 2 } } }} locale={props.localization.fullLocale}>\r\n      <React.Fragment>\r\n        {((status: EWidgetStatus) => {\r\n          switch (status) {\r\n            case EWidgetStatus.RENDERED:\r\n            case EWidgetStatus.UPDATING:\r\n              return props.children;\r\n            case EWidgetStatus.ERROR:\r\n              return <ErrorComponent details={props.errorHandlerProps} />;\r\n            default:\r\n              return props.placeholder;\r\n          }\r\n        })(props.widgetStatus)}\r\n      </React.Fragment>\r\n    </IntlProvider> : null);\r\n\r\n\r\nexport const ApplicationRootComponent = connect<Models.IBaseAppProps, {}, IApplicationRootProps>(\r\n  ({ localization, error, widgetStatus }: Models.IBaseStoreState) => ({\r\n    localization,\r\n    widgetStatus,\r\n    errorHandlerProps: error\r\n  }), {},\r\n  function (stateProps, despatchProps, ownProps) {\r\n    return {\r\n      ...(ownProps.propsfilter ? ownProps.propsfilter(stateProps) : stateProps),\r\n      ...despatchProps,\r\n      ...ownProps,\r\n    };\r\n  }\r\n)(Component);\r\n", "import * as React from \"react\";\r\nimport { LocalizationState } from \"bwtk\";\r\n\r\nfunction padBellCurrency(cents: string): string {\r\n  if (!cents) return \"00\";\r\n  if (cents.length === 1) return `${cents}0`;\r\n  return cents.slice(0, 2);\r\n}\r\n\r\nexport function getBellCurrency(localization: LocalizationState, value: any): string {\r\n  try {\r\n    const n: Array<string> = String(value).split(\".\");\r\n    let dollars = n[0];\r\n    const cents = n[1];\r\n    // let showCents = true;\r\n    const credit: boolean = value < 0 ? true : false;\r\n    dollars = dollars.replace(\"-\", \"\");\r\n    // If cents is undefined or null or empty, then dont show cents\r\n    if (Number(dollars) > 0 && (!cents || Number(cents) === 0)) {\r\n      // showCents = false;\r\n    }\r\n\r\n    switch (localization.locale) {\r\n      default:\r\n      case \"en\":\r\n        const enDollars = parseInt(dollars).toLocaleString(\"en\");\r\n        if (credit) {\r\n          return `CR $${enDollars}.${padBellCurrency(cents)}`;\r\n        } else {\r\n          return `$${enDollars}.${padBellCurrency(cents)}`;\r\n          // return `$${enDollars}${showCents ? \".\" + padBellCurrency(cents) : \"\"}`;\r\n        }\r\n      case \"fr\":\r\n        const frDollars = parseInt(dollars).toLocaleString(\"fr\");\r\n        if (credit) {\r\n          return `CR ${frDollars},${padBellCurrency(cents)}&nbsp;$`;\r\n        } else {\r\n          return `${frDollars},${padBellCurrency(cents)}&nbsp;$`;\r\n        }\r\n    }\r\n  } catch (e) {\r\n    // _log.error(e);\r\n    return value;\r\n  }\r\n}\r\n\r\nexport interface BellCurrencyProps {\r\n  value: any;\r\n  localization: any;\r\n  credit?: boolean;\r\n  className?: string;\r\n  tag?: string;\r\n  tagProps?: any;\r\n}\r\n\r\nconst Componenet = ({ value, className, localization, tag, tagProps, credit }: BellCurrencyProps) => {\r\n  const CustomTag = tag || \"span\";\r\n  return <CustomTag {...tagProps} className={`txtCurrency ${className || \"\"}`} dangerouslySetInnerHTML={{ __html: getBellCurrency(localization, value) }} />;\r\n};\r\n\r\nexport default Componenet;\r\n", "import * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport Componenet from \"./BellCurrency\";\r\n\r\n/**\r\n * Returns a <span /> containig the formatted currency in Bell specific format\r\n *\r\n * @param {any} value The raw number. Can be in any JS acceptable number format, or Number.\r\n * @param {string} className Additional class names for the element.\r\n *\r\n * @returns React component containig the formatted number string.\r\n * @type FC\r\n */\r\nexport interface BellCurrencyConnectedProps {\r\n  value: any;\r\n}\r\n\r\nexport const BellCurrencyComponent = (connect(\r\n  // Map state to props\r\n  ({ localization }: any) => ({ localization }),\r\n  // Map dispatch to props\r\n  (dispatch) => ({})\r\n)(Componenet as any) as any) as  React.FC<BellCurrencyConnectedProps>;\r\nBellCurrencyComponent.displayName = \"BellCurrency\";\r\n", "import * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { Context } from \"../../../Context\";\r\n\r\n\r\nexport const BrandedMessageComponent: React.FC<any> = (\r\n  { id, ...props }: any\r\n) => (<Context>{({ config }) => <FormattedMessage {...props} id={`${config.environmentVariables.brand}_${id}`} />}</Context>);\r\n", "import * as React from \"react\";\r\nimport { FormattedNumber, FormattedMessage } from \"react-intl\";\r\n\r\nexport interface ICurrencyComponetProps {\r\n  className?: string;\r\n  prefixClassName?: string;\r\n  fractionClassName?: string;\r\n  value: number;\r\n  monthly?: boolean;\r\n}\r\n\r\nexport interface ICurrencyComponet extends React.FC<ICurrencyComponetProps> {\r\n\r\n}\r\n\r\nconst Currency: React.FC<any> = ({\r\n  str,\r\n  prefixClassName,\r\n  fractionClassName\r\n}) => {\r\n  let whole: string = \"\", fraction: string = \"\", prefix: string = \"\";\r\n  if (str.indexOf(\"$\") === 0) {\r\n    const parts: Array<string> = str.split(\".\");\r\n    prefix = parts[0].substr(0, 1);\r\n    whole = parts[0].substr(1);\r\n    fraction = parts[1];\r\n  } else {\r\n    const parts: Array<string> = str.split(\",\");\r\n    whole = parts[0]; fraction = parts[1];\r\n  }\r\n  return <>\r\n    {Boolean(prefix) ? <sup className={prefixClassName}>{prefix}</sup> : null}\r\n    {whole}\r\n    <sup className={fractionClassName} aria-hidden>{fraction}</sup>\r\n    {fraction !== \"00\" && <span className=\"sr-only\">.{fraction} cents</span>}\r\n  </>;\r\n};\r\n\r\nexport const CurrencyComponent: ICurrencyComponet = ({\r\n  className,\r\n  prefixClassName,\r\n  fractionClassName,\r\n  value,\r\n  monthly\r\n}) => <FormattedNumber value={value} format=\"CAD\">\r\n  {\r\n    (str: string) => <span className={`formatted-currency ${className}`}>\r\n      <Currency str={str}\r\n        prefixClassName={prefixClassName}\r\n        fractionClassName={fractionClassName} />\r\n      {monthly ?\r\n        <sup className={prefixClassName}>\r\n          <FormattedMessage id=\"PER_MO\">{(per_mo) => <span aria-hidden=\"true\">{per_mo}</span>}</FormattedMessage>\r\n          <FormattedMessage id=\"PER_MONTH\">{(per_month) => <span className=\"sr-only\">{per_month}</span>}</FormattedMessage>\r\n        </sup> : null}\r\n    </span>\r\n  }\r\n</FormattedNumber>;\r\n\r\nCurrencyComponent.defaultProps = {\r\n  className: \"\",\r\n  prefixClassName: \"txtSize22\",\r\n  fractionClassName: \"txtSize22\"\r\n};\r\n", "export * from \"./BellCurrency\";\r\nexport * from \"./FormattedMessage\";\r\nexport * from \"./Currency\";\r\n", "function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport React, { PureComponent } from 'react'; // eslint-disable-line import/no-unresolved\n\nexport var PersistGate =\n/*#__PURE__*/\nfunction (_PureComponent) {\n  _inherits(PersistGate, _PureComponent);\n\n  function PersistGate() {\n    var _getPrototypeOf2;\n\n    var _this;\n\n    _classCallCheck(this, PersistGate);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _possibleConstructorReturn(this, (_getPrototypeOf2 = _getPrototypeOf(PersistGate)).call.apply(_getPrototypeOf2, [this].concat(args)));\n\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      bootstrapped: false\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_unsubscribe\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"handlePersistorState\", function () {\n      var persistor = _this.props.persistor;\n\n      var _persistor$getState = persistor.getState(),\n          bootstrapped = _persistor$getState.bootstrapped;\n\n      if (bootstrapped) {\n        if (_this.props.onBeforeLift) {\n          Promise.resolve(_this.props.onBeforeLift()).finally(function () {\n            return _this.setState({\n              bootstrapped: true\n            });\n          });\n        } else {\n          _this.setState({\n            bootstrapped: true\n          });\n        }\n\n        _this._unsubscribe && _this._unsubscribe();\n      }\n    });\n\n    return _this;\n  }\n\n  _createClass(PersistGate, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this._unsubscribe = this.props.persistor.subscribe(this.handlePersistorState);\n      this.handlePersistorState();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this._unsubscribe && this._unsubscribe();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (process.env.NODE_ENV !== 'production') {\n        if (typeof this.props.children === 'function' && this.props.loading) console.error('redux-persist: PersistGate expects either a function child or loading prop, but not both. The loading prop will be ignored.');\n      }\n\n      if (typeof this.props.children === 'function') {\n        return this.props.children(this.state.bootstrapped);\n      }\n\n      return this.state.bootstrapped ? this.props.children : this.props.loading;\n    }\n  }]);\n\n  return PersistGate;\n}(PureComponent);\n\n_defineProperty(PersistGate, \"defaultProps\", {\n  children: null,\n  loading: null\n});", "import * as React from \"react\";\r\nimport { persistStore } from \"redux-persist\";\r\nimport { PersistGate } from \"redux-persist/integration/react\";\r\n\r\nexport interface IProps {\r\n  render: Function;\r\n  store: any;\r\n}\r\n\r\n/*\r\n * consumer is the root render function of individual consuming widgets.\r\n * usage example:\r\n *\r\n *   import { Components } from \"omf-changepackage-components\";\r\n *   render(root: Element) {\r\n *    const { store } = this;\r\n *    ReactDOM.render(\r\n *     <ContextProvider value={{ config: this.config }}>\r\n *       <StoreProvider {...{ store }}>\r\n *         <Components.PersistGate render={() => <App />} store={store} />\r\n *       </StoreProvider>\r\n *     </ContextProvider>\r\n *     , root\r\n *    );\r\n *   }\r\n */\r\nexport const ReduxPersistGate: React.FC<IProps> = (props: IProps) => (\r\n  <PersistGate loading={null} persistor={persistStore(props.store)}>\r\n    {props.render()}\r\n  </PersistGate>\r\n);\r\n", "import * as React from \"react\";\r\nimport { ErrorComponent, IErrorComponent } from \"./Error\";\r\nimport { IContainerComponent, ContainerComponent, IPanelComponent, PanelComponent, IBRF3ContainerComponent, BRF3ContainerComponenet } from \"./Container\";\r\nimport { LightboxContainer } from \"./Lightbox\";\r\nimport { RestrictionModalView } from \"./Restriction\";\r\nimport { EllipsisText as EllipsisTextComponent, IEllipsisText } from \"./EllipsisText\";\r\nimport { ApplicationRootComponent, IApplicationRootProps } from \"./Application\";\r\nimport { CurrencyComponent, ICurrencyComponetProps, BellCurrencyComponent, BellCurrencyConnectedProps, BrandedMessageComponent } from \"./Localization\";\r\nimport { ReduxPersistGate } from \"./ReduxPersistGate\";\r\nimport { VisibleComponent } from \"./VisibilityContainer\";\r\n\r\nexport namespace Components {\r\n  export const Error: IErrorComponent = ErrorComponent;\r\n  export const Container: IContainerComponent = ContainerComponent;\r\n  export const Panel: IPanelComponent = PanelComponent;\r\n  export const BRF3Container: IBRF3ContainerComponent = BRF3ContainerComponenet;\r\n  export const Modal: typeof LightboxContainer = LightboxContainer;\r\n  export const RestrictionModal: typeof RestrictionModalView = RestrictionModalView;\r\n  export const ApplicationRoot: React.FC<IApplicationRootProps> = ApplicationRootComponent as any;\r\n  export const EllipsisText: IEllipsisText = EllipsisTextComponent;\r\n  export const Currency: React.FC<ICurrencyComponetProps> = CurrencyComponent;\r\n  export const BellCurrency: React.FC<BellCurrencyConnectedProps> = BellCurrencyComponent;\r\n  export const BrandedMessage: React.FC<any> = BrandedMessageComponent;\r\n  export const PersistGate: typeof ReduxPersistGate = ReduxPersistGate;\r\n  export const Visible: typeof VisibleComponent = VisibleComponent;\r\n}\r\n", "export * from \"./Actions\";\r\nexport * from \"./Client\";\r\nexport * from \"./Context\";\r\nexport * from \"./Epics\";\r\nexport * from \"./Models\";\r\nexport * from \"./Reducers\";\r\nexport * from \"./Utils\";\r\nexport * from \"./ViewComponents\";\r\nexport * from \"./Omniture\";\r\n"], "names": [], "sourceRoot": ""}